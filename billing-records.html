<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收费记录 - 智慧停车场系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div id="header"></div>
    <div id="sidebar"></div>
    
    <main class="ml-0 lg:ml-64 pt-16 p-6">
        <div class="max-w-7xl mx-auto">
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h1 class="text-2xl font-bold">收费记录</h1>
                    <p class="text-gray-400">查看和管理所有收费交易记录</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索车牌号..." 
                               class="input-glass pr-10" 
                               onkeypress="if(event.key==='Enter')billingRecords.search()">
                        <svg class="w-5 h-5 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" 
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <button onclick="billingRecords.exportData()" class="btn-secondary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        导出数据
                    </button>
                </div>
            </div>

            <!-- 统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" id="statsCards">
                <!-- 统计卡片将通过JS动态生成 -->
            </div>

            <!-- 筛选器 -->
            <div class="glass-card p-6 rounded-2xl mb-8">
                <h2 class="text-xl font-semibold mb-4">筛选条件</h2>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">日期范围</label>
                        <select class="input-glass w-full" id="dateRange">
                            <option value="today">今天</option>
                            <option value="yesterday">昨天</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                            <option value="all">全部</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">停车场</label>
                        <select class="input-glass w-full" id="parkingFilter">
                            <option value="">全部停车场</option>
                            <!-- 停车场选项将通过JS动态生成 -->
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">金额范围</label>
                        <select class="input-glass w-full" id="amountRange">
                            <option value="">全部金额</option>
                            <option value="0-10">0-10元</option>
                            <option value="10-50">10-50元</option>
                            <option value="50-100">50-100元</option>
                            <option value="100+">100元以上</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button onclick="billingRecords.applyFilters()" class="btn-primary w-full">
                            应用筛选
                        </button>
                    </div>
                </div>
            </div>

            <!-- 交易记录表格 -->
            <div class="glass-card p-6 rounded-2xl">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold">交易记录</h2>
                    <div class="text-sm text-gray-400" id="recordCount">
                        共 0 条记录
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr class="border-b border-gray-700">
                                <th class="pb-3 text-left text-sm font-medium text-gray-300">交易时间</th>
                                <th class="pb-3 text-left text-sm font-medium text-gray-300">车牌号码</th>
                                <th class="pb-3 text-left text-sm font-medium text-gray-300">停车场</th>
                                <th class="pb-3 text-left text-sm font-medium text-gray-300">停车时长</th>
                                <th class="pb-3 text-right text-sm font-medium text-gray-300">金额</th>
                                <th class="pb-3 text-center text-sm font-medium text-gray-300">状态</th>
                            </tr>
                        </thead>
                        <tbody id="recordsTable">
                            <!-- 记录将通过JS动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页控件 -->
                <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-700">
                    <div class="text-sm text-gray-400" id="pageInfo">
                        第 1 页，共 1 页
                    </div>
                    <div class="flex space-x-2" id="pagination">
                        <!-- 分页按钮将通过JS动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 交易详情模态框 -->
    <div id="detailModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden items-center justify-center z-50 p-4">
        <div class="glass-card p-6 rounded-2xl w-full max-w-2xl">
            <h3 class="text-xl font-semibold mb-4">交易详情</h3>
            <div id="transactionDetails">
                <!-- 详情内容将通过JS动态生成 -->
            </div>
            <div class="flex justify-end pt-4">
                <button onclick="closeDetailModal()" class="btn-secondary">关闭</button>
            </div>
        </div>
    </div>

    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/billing-records.js"></script>
</body>
</html>