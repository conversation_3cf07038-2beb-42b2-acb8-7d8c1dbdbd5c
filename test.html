<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - 停车场系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="js/storage.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <h1 class="text-2xl font-bold mb-6">停车场系统功能测试</h1>
    
    <div class="space-y-4">
        <button onclick="testStorage()" class="bg-blue-500 text-white px-4 py-2 rounded">
            测试数据存储
        </button>
        
        <button onclick="testParking()" class="bg-green-500 text-white px-4 py-2 rounded">
            测试停车场管理
        </button>
        
        <button onclick="clearData()" class="bg-red-500 text-white px-4 py-2 rounded">
            清空测试数据
        </button>
    </div>
    
    <div id="output" class="mt-6 p-4 bg-white rounded shadow">
        <p>测试结果将显示在这里...</p>
    </div>

    <script>
    function testStorage() {
        const output = document.getElementById('output');
        output.innerHTML = '<p>测试数据存储功能...</p>';
        
        // 测试停车场添加
        const parking = storage.addParkingLot({
            name: '测试停车场',
            location: '测试位置',
            totalSpaces: 20,
            hourlyRate: 5
        });
        
        // 测试车辆添加
        const vehicle = storage.addVehicle({
            licensePlate: '测试A12345',
            parkingLotId: parking.id
        });
        
        // 测试交易记录
        storage.addTransaction({
            licensePlate: '测试A12345',
            parkingLotId: parking.id,
            amount: 15.5,
            status: 'completed'
        });
        
        // 读取数据验证
        const parkingLots = storage.getParkingLots();
        const vehicles = storage.getVehicles();
        const transactions = storage.getTransactions();
        
        output.innerHTML += `
            <p>✅ 停车场数量: ${parkingLots.length}</p>
            <p>✅ 车辆记录: ${vehicles.length}</p>
            <p>✅ 交易记录: ${transactions.length}</p>
            <p>✅ 测试数据创建成功！</p>
        `;
    }
    
    function testParking() {
        const output = document.getElementById('output');
        output.innerHTML = '<p>测试停车场统计功能...</p>';
        
        const parkingLots = storage.getParkingLots();
        if (parkingLots.length === 0) {
            output.innerHTML += '<p>❌ 请先创建测试数据</p>';
            return;
        }
        
        const parking = parkingLots[0];
        const stats = storage.getParkingLotStats(parking.id);
        const todayStats = storage.getTodayStats();
        
        output.innerHTML += `
            <p>✅ 停车场: ${parking.name}</p>
            <p>✅ 总车位: ${stats.totalSpaces}</p>
            <p>✅ 已占用: ${stats.occupiedSpaces}</p>
            <p>✅ 使用率: ${stats.usageRate}%</p>
            <p>✅ 今日车流量: ${todayStats.vehicleCount}</p>
            <p>✅ 今日收入: ¥${todayStats.revenue.toFixed(2)}</p>
        `;
    }
    
    function clearData() {
        localStorage.clear();
        storage.initializeStorage();
        document.getElementById('output').innerHTML = '<p>✅ 数据已清空</p>';
    }
    </script>
</body>
</html>