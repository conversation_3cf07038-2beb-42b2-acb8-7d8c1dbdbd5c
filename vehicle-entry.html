<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>车辆进出管理 - 智慧停车场系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css" />
  </head>
  <body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div id="header"></div>
    <div id="sidebar"></div>

    <main class="ml-0 lg:ml-64 pt-16 p-6">
      <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-8">
          <div>
            <h1 class="text-2xl font-bold" id="pageTitle">车辆入场</h1>
            <p class="text-gray-400" id="pageSubtitle">记录车辆进入停车场</p>
          </div>
          <div class="flex space-x-4">
            <button id="switchModeBtn" class="btn-secondary">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                ></path>
              </svg>
              切换到出场
            </button>
          </div>
        </div>

        <!-- 操作面板 -->
        <div class="glass-card p-6 rounded-2xl mb-8">
          <h2 class="text-xl font-semibold mb-6" id="formTitle">车辆入场登记</h2>

          <form id="vehicleForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">车牌号码</label>
                <div class="license-plate-input">
                  <div class="flex items-center space-x-2">
                    <!-- 省份显示区域 -->
                    <div class="license-segment">
                      <div
                        id="provincePart"
                        class="license-display-area text-2xl font-mono text-center"
                        style="width: 60px; height: 50px"
                        data-placeholder="省"
                        data-type="province"
                      >
                        省
                      </div>
                    </div>

                    <!-- 字母显示区域 -->
                    <div class="license-segment">
                      <div
                        id="letterPart"
                        class="license-display-area text-2xl font-mono text-center"
                        style="width: 60px; height: 50px"
                        data-placeholder="字"
                        data-type="letter"
                      >
                        字
                      </div>
                    </div>

                    <!-- 数字和字母显示区域 -->
                    <div class="license-segment flex-1">
                      <div
                        id="numberPart"
                        class="license-display-area w-full text-2xl font-mono text-center"
                        style="height: 50px"
                        data-placeholder="12345"
                        data-type="number"
                      >
                        12345
                      </div>
                    </div>

                    <!-- 虚拟键盘触发按钮 -->
                    <button
                      type="button"
                      id="keyboardToggle"
                      class="license-segment input-glass text-2xl font-mono flex items-center justify-center"
                      style="width: 60px"
                    >
                      ⌨️
                    </button>
                  </div>
                  <!-- 隐藏的完整车牌号输入框，用于向后兼容 -->
                  <input type="hidden" id="licensePlate" required />
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">选择停车场</label>
                <div id="parkingSelector">
                  <!-- 停车场选择器将通过JS动态生成 -->
                </div>
              </div>
            </div>

            <!-- 入场时间（自动生成） -->
            <div id="entryTimeField">
              <label class="block text-sm font-medium text-gray-300 mb-2">入场时间</label>
              <div class="input-glass w-full">
                <span id="currentTime">--:--:--</span>
              </div>
            </div>

            <!-- 出场时间和费用计算（出场模式显示） -->
            <div id="exitSection" class="hidden space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-300 mb-2">入场时间</label>
                  <div class="input-glass w-full" id="entryTimeDisplay">--:--:--</div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-300 mb-2">出场时间</label>
                  <div class="input-glass w-full">
                    <span id="exitTime">--:--:--</span>
                  </div>
                </div>
              </div>

              <div class="bg-gray-800/50 rounded-lg p-4">
                <h3 class="text-lg font-semibold mb-3 text-yellow-400">费用计算</h3>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-gray-300">停车时长</span>
                    <span class="font-mono" id="parkingDuration">0小时0分钟</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-300">小时费率</span>
                    <span class="font-mono" id="hourlyRate">¥0.00/小时</span>
                  </div>
                  <div class="border-t border-gray-700 pt-2">
                    <div class="flex justify-between text-lg font-bold">
                      <span class="text-white">应付金额</span>
                      <span class="text-green-400" id="totalAmount">¥0.00</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex space-x-4 pt-4">
              <button
                type="button"
                onclick="location.href='index.html'"
                class="btn-secondary flex-1"
              >
                返回首页
              </button>
              <button type="submit" class="btn-primary flex-1" id="submitBtn">
                <span id="submitText">确认入场</span>
              </button>
            </div>
          </form>
        </div>

        <!-- 最近记录 -->
        <div class="glass-card p-6 rounded-2xl">
          <h2 class="text-xl font-semibold mb-4">最近车辆记录</h2>
          <div id="recentRecords" class="space-y-3">
            <!-- 最近记录将通过JS动态生成 -->
          </div>
        </div>
      </div>
    </main>

    <!-- 虚拟键盘遮罩层和键盘容器 -->
    <div id="keyboardOverlay" class="keyboard-overlay hidden"></div>
    <div id="virtualKeyboard" class="virtual-keyboard glass-card hidden">
      <div class="keyboard-section">
        <div class="keyboard-row">
          <button class="key-btn" data-value="京">京</button>
          <button class="key-btn" data-value="津">津</button>
          <button class="key-btn" data-value="冀">冀</button>
          <button class="key-btn" data-value="晋">晋</button>
          <button class="key-btn" data-value="蒙">蒙</button>
          <button class="key-btn" data-value="辽">辽</button>
          <button class="key-btn" data-value="吉">吉</button>
          <button class="key-btn" data-value="黑">黑</button>
        </div>
        <div class="keyboard-row">
          <button class="key-btn" data-value="沪">沪</button>
          <button class="key-btn" data-value="苏">苏</button>
          <button class="key-btn" data-value="浙">浙</button>
          <button class="key-btn" data-value="皖">皖</button>
          <button class="key-btn" data-value="闽">闽</button>
          <button class="key-btn" data-value="赣">赣</button>
          <button class="key-btn" data-value="鲁">鲁</button>
          <button class="key-btn" data-value="豫">豫</button>
        </div>
        <div class="keyboard-row">
          <button class="key-btn" data-value="鄂">鄂</button>
          <button class="key-btn" data-value="湘">湘</button>
          <button class="key-btn" data-value="粤">粤</button>
          <button class="key-btn" data-value="桂">桂</button>
          <button class="key-btn" data-value="琼">琼</button>
          <button class="key-btn" data-value="渝">渝</button>
          <button class="key-btn" data-value="川">川</button>
          <button class="key-btn" data-value="贵">贵</button>
        </div>
        <div class="keyboard-row">
          <button class="key-btn" data-value="云">云</button>
          <button class="key-btn" data-value="藏">藏</button>
          <button class="key-btn" data-value="陕">陕</button>
          <button class="key-btn" data-value="甘">甘</button>
          <button class="key-btn" data-value="青">青</button>
          <button class="key-btn" data-value="宁">宁</button>
          <button class="key-btn" data-value="新">新</button>
          <button class="key-btn" data-value="港">港</button>
        </div>
        <div class="keyboard-row">
          <button class="key-btn" data-value="澳">澳</button>
          <button class="key-btn" data-value="台">台</button>
        </div>
      </div>

      <div class="keyboard-section">
        <div class="keyboard-row">
          <button class="key-btn" data-value="A">A</button>
          <button class="key-btn" data-value="B">B</button>
          <button class="key-btn" data-value="C">C</button>
          <button class="key-btn" data-value="D">D</button>
          <button class="key-btn" data-value="E">E</button>
          <button class="key-btn" data-value="F">F</button>
          <button class="key-btn" data-value="G">G</button>
          <button class="key-btn" data-value="H">H</button>
        </div>
        <div class="keyboard-row">
          <button class="key-btn" data-value="J">J</button>
          <button class="key-btn" data-value="K">K</button>
          <button class="key-btn" data-value="L">L</button>
          <button class="key-btn" data-value="M">M</button>
          <button class="key-btn" data-value="N">N</button>
          <button class="key-btn" data-value="P">P</button>
          <button class="key-btn" data-value="Q">Q</button>
          <button class="key-btn" data-value="R">R</button>
        </div>
        <div class="keyboard-row">
          <button class="key-btn" data-value="S">S</button>
          <button class="key-btn" data-value="T">T</button>
          <button class="key-btn" data-value="U">U</button>
          <button class="key-btn" data-value="V">V</button>
          <button class="key-btn" data-value="W">W</button>
          <button class="key-btn" data-value="X">X</button>
          <button class="key-btn" data-value="Y">Y</button>
          <button class="key-btn" data-value="Z">Z</button>
        </div>
      </div>

      <div class="keyboard-section">
        <div class="keyboard-row">
          <button class="key-btn" data-value="1">1</button>
          <button class="key-btn" data-value="2">2</button>
          <button class="key-btn" data-value="3">3</button>
          <button class="key-btn" data-value="4">4</button>
          <button class="key-btn" data-value="5">5</button>
          <button class="key-btn" data-value="6">6</button>
          <button class="key-btn" data-value="7">7</button>
          <button class="key-btn" data-value="8">8</button>
        </div>
        <div class="keyboard-row">
          <button class="key-btn" data-value="9">9</button>
          <button class="key-btn" data-value="0">0</button>
          <button class="key-btn special" data-action="backspace">←</button>
          <button class="key-btn special" data-action="clear">清空</button>
          <button class="key-btn primary" data-action="done">完成</button>
        </div>
      </div>
    </div>
                    <div class="keyboard-section">
                      <div class="keyboard-row">
                        <button class="key-btn" data-value="京">京</button>
                        <button class="key-btn" data-value="津">津</button>
                        <button class="key-btn" data-value="冀">冀</button>
                        <button class="key-btn" data-value="晋">晋</button>
                        <button class="key-btn" data-value="蒙">蒙</button>
                        <button class="key-btn" data-value="辽">辽</button>
                        <button class="key-btn" data-value="吉">吉</button>
                        <button class="key-btn" data-value="黑">黑</button>
                      </div>
                      <div class="keyboard-row">
                        <button class="key-btn" data-value="沪">沪</button>
                        <button class="key-btn" data-value="苏">苏</button>
                        <button class="key-btn" data-value="浙">浙</button>
                        <button class="key-btn" data-value="皖">皖</button>
                        <button class="key-btn" data-value="闽">闽</button>
                        <button class="key-btn" data-value="赣">赣</button>
                        <button class="key-btn" data-value="鲁">鲁</button>
                        <button class="key-btn" data-value="豫">豫</button>
                      </div>
                      <div class="keyboard-row">
                        <button class="key-btn" data-value="鄂">鄂</button>
                        <button class="key-btn" data-value="湘">湘</button>
                        <button class="key-btn" data-value="粤">粤</button>
                        <button class="key-btn" data-value="桂">桂</button>
                        <button class="key-btn" data-value="琼">琼</button>
                        <button class="key-btn" data-value="渝">渝</button>
                        <button class="key-btn" data-value="川">川</button>
                        <button class="key-btn" data-value="贵">贵</button>
                      </div>
                      <div class="keyboard-row">
                        <button class="key-btn" data-value="云">云</button>
                        <button class="key-btn" data-value="藏">藏</button>
                        <button class="key-btn" data-value="陕">陕</button>
                        <button class="key-btn" data-value="甘">甘</button>
                        <button class="key-btn" data-value="青">青</button>
                        <button class="key-btn" data-value="宁">宁</button>
                        <button class="key-btn" data-value="新">新</button>
                        <button class="key-btn" data-value="港">港</button>
                      </div>
                      <div class="keyboard-row">
                        <button class="key-btn" data-value="澳">澳</button>
                        <button class="key-btn" data-value="台">台</button>
                      </div>
                    </div>

                    <div class="keyboard-section">
                      <div class="keyboard-row">
                        <button class="key-btn" data-value="A">A</button>
                        <button class="key-btn" data-value="B">B</button>
                        <button class="key-btn" data-value="C">C</button>
                        <button class="key-btn" data-value="D">D</button>
                        <button class="key-btn" data-value="E">E</button>
                        <button class="key-btn" data-value="F">F</button>
                        <button class="key-btn" data-value="G">G</button>
                        <button class="key-btn" data-value="H">H</button>
                      </div>
                      <div class="keyboard-row">
                        <button class="key-btn" data-value="J">J</button>
                        <button class="key-btn" data-value="K">K</button>
                        <button class="key-btn" data-value="L">L</button>
                        <button class="key-btn" data-value="M">M</button>
                        <button class="key-btn" data-value="N">N</button>
                        <button class="key-btn" data-value="P">P</button>
                        <button class="key-btn" data-value="Q">Q</button>
                        <button class="key-btn" data-value="R">R</button>
                      </div>
                      <div class="keyboard-row">
                        <button class="key-btn" data-value="S">S</button>
                        <button class="key-btn" data-value="T">T</button>
                        <button class="key-btn" data-value="U">U</button>
                        <button class="key-btn" data-value="V">V</button>
                        <button class="key-btn" data-value="W">W</button>
                        <button class="key-btn" data-value="X">X</button>
                        <button class="key-btn" data-value="Y">Y</button>
                        <button class="key-btn" data-value="Z">Z</button>
                      </div>
                    </div>

                    <div class="keyboard-section">
                      <div class="keyboard-row">
                        <button class="key-btn" data-value="1">1</button>
                        <button class="key-btn" data-value="2">2</button>
                        <button class="key-btn" data-value="3">3</button>
                        <button class="key-btn" data-value="4">4</button>
                        <button class="key-btn" data-value="5">5</button>
                        <button class="key-btn" data-value="6">6</button>
                        <button class="key-btn" data-value="7">7</button>
                        <button class="key-btn" data-value="8">8</button>
                      </div>
                      <div class="keyboard-row">
                        <button class="key-btn" data-value="9">9</button>
                        <button class="key-btn" data-value="0">0</button>
                        <button class="key-btn special" data-action="backspace">←</button>
                        <button class="key-btn special" data-action="clear">清空</button>
                        <button class="key-btn primary" data-action="done">完成</button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">选择停车场</label>
                <div id="parkingSelector">
                  <!-- 停车场选择器将通过JS动态生成 -->
                </div>
              </div>
            </div>

            <!-- 入场时间（自动生成） -->
            <div id="entryTimeField">
              <label class="block text-sm font-medium text-gray-300 mb-2">入场时间</label>
              <div class="input-glass w-full">
                <span id="currentTime">--:--:--</span>
              </div>
            </div>

            <!-- 出场时间和费用计算（出场模式显示） -->
            <div id="exitSection" class="hidden space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-300 mb-2">入场时间</label>
                  <div class="input-glass w-full" id="entryTimeDisplay">--:--:--</div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-300 mb-2">出场时间</label>
                  <div class="input-glass w-full">
                    <span id="exitTime">--:--:--</span>
                  </div>
                </div>
              </div>

              <div class="bg-gray-800/50 rounded-lg p-4">
                <h3 class="text-lg font-semibold mb-3 text-yellow-400">费用计算</h3>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-gray-300">停车时长</span>
                    <span class="font-mono" id="parkingDuration">0小时0分钟</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-gray-300">小时费率</span>
                    <span class="font-mono" id="hourlyRate">¥0.00/小时</span>
                  </div>
                  <div class="border-t border-gray-700 pt-2">
                    <div class="flex justify-between text-lg font-bold">
                      <span class="text-white">应付金额</span>
                      <span class="text-green-400" id="totalAmount">¥0.00</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex space-x-4 pt-4">
              <button
                type="button"
                onclick="location.href='index.html'"
                class="btn-secondary flex-1"
              >
                返回首页
              </button>
              <button type="submit" class="btn-primary flex-1" id="submitBtn">
                <span id="submitText">确认入场</span>
              </button>
            </div>
          </form>
        </div>

        <!-- 最近记录 -->
        <div class="glass-card p-6 rounded-2xl">
          <h2 class="text-xl font-semibold mb-4">最近车辆记录</h2>
          <div id="recentRecords" class="space-y-3">
            <!-- 最近记录将通过JS动态生成 -->
          </div>
        </div>
      </div>
    </main>

    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/vehicle-entry.js"></script>
  </body>
</html>
