// 测试z-index修复效果的脚本

window.testZIndexFix = function() {
    console.log('=== 测试 z-index 修复效果 ===');
    
    const dropdown = document.getElementById('parkingDropdown');
    
    if (!dropdown) {
        console.error('❌ 下拉菜单元素不存在');
        return;
    }
    
    // 检查当前的z-index
    const computedStyle = window.getComputedStyle(dropdown);
    console.log('当前样式检查:');
    console.log(`  - z-index: ${computedStyle.zIndex}`);
    console.log(`  - position: ${computedStyle.position}`);
    console.log(`  - 类名: ${dropdown.className}`);
    
    // 检查是否包含z-50类
    const hasZIndex = dropdown.classList.contains('z-50');
    console.log(`  - 包含 z-50 类: ${hasZIndex}`);
    
    if (!hasZIndex) {
        console.log('⚠️ 缺少 z-50 类，手动添加...');
        dropdown.classList.add('z-50');
        console.log('✅ 已添加 z-50 类');
    }
    
    // 显示下拉菜单进行测试
    console.log('显示下拉菜单进行测试...');
    dropdown.classList.remove('opacity-0', 'invisible', 'scale-95');
    dropdown.classList.add('opacity-100', 'visible', 'scale-100');
    
    // 检查是否被遮挡
    const rect = dropdown.getBoundingClientRect();
    console.log('下拉菜单位置信息:');
    console.log(`  - top: ${rect.top}`);
    console.log(`  - left: ${rect.left}`);
    console.log(`  - width: ${rect.width}`);
    console.log(`  - height: ${rect.height}`);
    console.log(`  - 是否在视口内: ${rect.top >= 0 && rect.left >= 0 && rect.bottom <= window.innerHeight && rect.right <= window.innerWidth}`);
    
    // 检查是否有其他元素遮挡
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    const elementAtCenter = document.elementFromPoint(centerX, centerY);
    
    console.log('遮挡检查:');
    console.log(`  - 中心点坐标: (${centerX}, ${centerY})`);
    console.log(`  - 中心点元素: ${elementAtCenter ? elementAtCenter.tagName + (elementAtCenter.id ? '#' + elementAtCenter.id : '') : 'null'}`);
    console.log(`  - 是否被遮挡: ${elementAtCenter && !dropdown.contains(elementAtCenter)}`);
    
    // 5秒后自动隐藏
    setTimeout(() => {
        console.log('自动隐藏下拉菜单');
        dropdown.classList.add('opacity-0', 'invisible', 'scale-95');
        dropdown.classList.remove('opacity-100', 'visible', 'scale-100');
        console.log('=== 测试完成 ===');
    }, 5000);
};

// 添加一个强制修复z-index的函数
window.forceFixZIndex = function() {
    console.log('=== 强制修复 z-index ===');
    
    const dropdown = document.getElementById('parkingDropdown');
    
    if (dropdown) {
        // 确保有z-50类
        if (!dropdown.classList.contains('z-50')) {
            dropdown.classList.add('z-50');
            console.log('✅ 添加了 z-50 类');
        }
        
        // 也可以直接设置内联样式作为备用
        dropdown.style.zIndex = '9999';
        console.log('✅ 设置了内联 z-index: 9999');
        
        // 检查最终效果
        const computedStyle = window.getComputedStyle(dropdown);
        console.log(`最终 z-index: ${computedStyle.zIndex}`);
        
        console.log('=== 强制修复完成 ===');
    } else {
        console.error('❌ 下拉菜单元素不存在');
    }
};

// 页面加载后自动检查
window.addEventListener('load', function() {
    setTimeout(() => {
        console.log('自动检查 z-index 修复效果...');
        testZIndexFix();
    }, 3000);
});

console.log('z-index 修复测试脚本已加载');
console.log('可以运行以下命令:');
console.log('  - testZIndexFix() // 测试修复效果');
console.log('  - forceFixZIndex() // 强制修复z-index');
