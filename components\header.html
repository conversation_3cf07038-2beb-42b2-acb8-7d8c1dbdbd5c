<header
  class="fixed top-0 left-0 right-0 bg-gray-900/80 backdrop-blur-md border-b border-gray-700 z-50"
>
  <div class="flex items-center justify-between px-6 py-4">
    <div class="flex items-center space-x-4">
      <button id="sidebarToggle" class="lg:hidden text-gray-300 hover:text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16M4 18h16"
          ></path>
        </svg>
      </button>
      <h1 class="text-xl font-bold text-white">
        <span class="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
          智慧停车场管理系统
        </span>
      </h1>
    </div>

    <div class="flex items-center space-x-4">
      <!-- 停车场切换器 -->
      <div class="relative" id="parkingSwitcher">
        <button
          class="flex items-center space-x-2 px-3 py-2 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
        >
          <span class="text-sm text-gray-300" id="currentParkingName">选择停车场</span>
          <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 9l-7 7-7-7"
            ></path>
          </svg>
        </button>

        <div
          class="absolute right-0 mt-2 w-64 bg-gray-800 rounded-lg shadow-lg opacity-0 invisible transition-all duration-200 transform scale-95"
          id="parkingDropdown"
          style="z-index: 9999"
        >
          <div class="py-2 max-h-60 overflow-y-auto" id="parkingListDropdown">
            <!-- 停车场列表将通过JS动态生成 -->
          </div>
        </div>
      </div>

      <div class="hidden md:flex items-center space-x-2 text-sm text-gray-300">
        <div class="status-indicator status-active"></div>
        <span>系统运行中</span>
      </div>

      <div class="relative">
        <button
          class="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-gray-600 transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            ></path>
          </svg>
        </button>

        <div
          class="absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg opacity-0 invisible transition-all duration-200 transform scale-95"
          style="z-index: 9999"
        >
          <div class="py-2">
            <a href="#" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">个人资料</a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">系统设置</a>
            <div class="border-t border-gray-700 my-1"></div>
            <a href="#" class="block px-4 py-2 text-sm text-red-400 hover:bg-gray-700">退出登录</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>
