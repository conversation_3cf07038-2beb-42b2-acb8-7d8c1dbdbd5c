class BillingRecords {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.currentFilters = {};
        this.init();
    }

    init() {
        this.loadParkingFilter();
        this.loadStats();
        this.loadRecords();
        this.setupEventListeners();
        this.checkUrlParams();
    }

    loadParkingFilter() {
        const container = document.getElementById('parkingFilter');
        const parkingLots = storage.getParkingLots();
        
        let html = '<option value="">全部停车场</option>';
        const currentParkingId = storage.getCurrentParkingLotId();
        
        parkingLots.forEach(parking => {
            const selected = parking.id === currentParkingId ? 'selected' : '';
            html += `<option value="${parking.id}" ${selected}>${parking.name}</option>`;
        });
        
        container.innerHTML = html;
        
        // 如果有当前停车场，默认选择它
        if (currentParkingId) {
            container.value = currentParkingId;
            this.currentFilters.parkingLotId = currentParkingId;
        }
    }

    loadStats() {
        const currentParkingId = storage.getCurrentParkingLotId();
        const transactions = currentParkingId ? storage.getTransactionsByParkingLot(currentParkingId) : storage.getTransactions();
        const today = new Date().toDateString();
        
        const todayTransactions = transactions.filter(t => 
            new Date(t.createdAt).toDateString() === today
        );
        
        const totalRevenue = transactions.reduce((sum, t) => sum + t.amount, 0);
        const todayRevenue = todayTransactions.reduce((sum, t) => sum + t.amount, 0);
        const avgTransaction = transactions.length > 0 ? totalRevenue / transactions.length : 0;
        
        const stats = [
            {
                title: '总交易额',
                value: `¥${totalRevenue.toFixed(2)}`,
                icon: '💰',
                color: 'bg-green-500/20',
                textColor: 'text-green-400'
            },
            {
                title: '今日收入',
                value: `¥${todayRevenue.toFixed(2)}`,
                icon: '📈',
                color: 'bg-blue-500/20',
                textColor: 'text-blue-400'
            },
            {
                title: '总交易数',
                value: transactions.length,
                icon: '📋',
                color: 'bg-purple-500/20',
                textColor: 'text-purple-400'
            },
            {
                title: '平均交易',
                value: `¥${avgTransaction.toFixed(2)}`,
                icon: '⚖️',
                color: 'bg-yellow-500/20',
                textColor: 'text-yellow-400'
            }
        ];

        let html = '';
        stats.forEach(stat => {
            html += `
                <div class="glass-card p-4 rounded-xl">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-300 text-sm">${stat.title}</p>
                            <h2 class="text-2xl font-bold ${stat.textColor}">${stat.value}</h2>
                        </div>
                        <div class="w-10 h-10 ${stat.color} rounded-full flex items-center justify-center">
                            <span class="text-lg">${stat.icon}</span>
                        </div>
                    </div>
                </div>
            `;
        });

        document.getElementById('statsCards').innerHTML = html;
    }

    loadRecords() {
        let transactions = storage.getTransactions();
        
        // 应用筛选
        transactions = this.applyFiltersToData(transactions);
        
        // 分页计算
        this.totalPages = Math.ceil(transactions.length / this.pageSize);
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const paginatedData = transactions.slice(startIndex, startIndex + this.pageSize);
        
        this.updateTable(paginatedData);
        this.updatePagination(transactions.length);
    }

    applyFiltersToData(data) {
        let filtered = [...data];
        
        // 日期筛选
        const dateRange = document.getElementById('dateRange').value;
        if (dateRange && dateRange !== 'all') {
            const now = new Date();
            filtered = filtered.filter(t => {
                const transDate = new Date(t.createdAt);
                
                switch (dateRange) {
                    case 'today':
                        return transDate.toDateString() === now.toDateString();
                    case 'yesterday':
                        const yesterday = new Date(now);
                        yesterday.setDate(yesterday.getDate() - 1);
                        return transDate.toDateString() === yesterday.toDateString();
                    case 'week':
                        const weekStart = new Date(now);
                        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
                        return transDate >= weekStart;
                    case 'month':
                        return transDate.getMonth() === now.getMonth() && 
                               transDate.getFullYear() === now.getFullYear();
                    default:
                        return true;
                }
            });
        }
        
        // 停车场筛选
        const parkingFilter = document.getElementById('parkingFilter').value;
        if (parkingFilter) {
            filtered = filtered.filter(t => t.parkingLotId === parkingFilter);
        }
        
        // 金额筛选
        const amountRange = document.getElementById('amountRange').value;
        if (amountRange) {
            filtered = filtered.filter(t => {
                const amount = t.amount;
                switch (amountRange) {
                    case '0-10': return amount <= 10;
                    case '10-50': return amount > 10 && amount <= 50;
                    case '50-100': return amount > 50 && amount <= 100;
                    case '100+': return amount > 100;
                    default: return true;
                }
            });
        }
        
        // 搜索筛选
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        if (searchTerm) {
            filtered = filtered.filter(t => 
                t.licensePlate.toLowerCase().includes(searchTerm)
            );
        }
        
        return filtered;
    }

    updateTable(transactions) {
        const container = document.getElementById('recordsTable');
        
        if (transactions.length === 0) {
            container.innerHTML = `
                <tr>
                    <td colspan="6" class="py-8 text-center text-gray-400">
                        <div class="text-4xl mb-2">📋</div>
                        <p>暂无交易记录</p>
                    </td>
                </tr>
            `;
            return;
        }

        let html = '';
        transactions.forEach(transaction => {
            const transDate = new Date(transaction.createdAt);
            const parking = this.getParkingName(transaction.parkingLotId);
            const duration = this.calculateDuration(transaction.entryTime, transaction.exitTime);
            
            html += `
                <tr class="border-b border-gray-700 hover:bg-gray-800/50 cursor-pointer"
                    onclick="billingRecords.showDetail('${transaction.id}')">
                    <td class="py-4 text-sm">
                        <div>${transDate.toLocaleDateString('zh-CN')}</div>
                        <div class="text-gray-400 text-xs">${transDate.toLocaleTimeString('zh-CN')}</div>
                    </td>
                    <td class="py-4">
                        <span class="font-mono text-sm">${transaction.licensePlate}</span>
                    </td>
                    <td class="py-4 text-sm text-gray-300">${parking}</td>
                    <td class="py-4 text-sm text-gray-300">${duration}</td>
                    <td class="py-4 text-right">
                        <span class="text-green-400 font-semibold">¥${transaction.amount.toFixed(2)}</span>
                    </td>
                    <td class="py-4 text-center">
                        <span class="px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full">
                            已完成
                        </span>
                    </td>
                </tr>
            `;
        });

        container.innerHTML = html;
    }

    updatePagination(totalRecords) {
        document.getElementById('recordCount').textContent = `共 ${totalRecords} 条记录`;
        document.getElementById('pageInfo').textContent = `第 ${this.currentPage} 页，共 ${this.totalPages} 页`;
        
        const container = document.getElementById('pagination');
        
        let html = '';
        
        // 上一页按钮
        html += `
            <button onclick="billingRecords.previousPage()" 
                    class="px-3 py-1 rounded border border-gray-600 text-gray-300 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    ${this.currentPage === 1 ? 'disabled' : ''}>
                上一页
            </button>
        `;
        
        // 页码按钮
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(this.totalPages, startPage + 4);
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <button onclick="billingRecords.goToPage(${i})" 
                        class="px-3 py-1 rounded border ${
                            i === this.currentPage 
                            ? 'bg-blue-600 border-blue-600 text-white' 
                            : 'border-gray-600 text-gray-300 hover:bg-gray-700'
                        }">
                    ${i}
                </button>
            `;
        }
        
        // 下一页按钮
        html += `
            <button onclick="billingRecords.nextPage()" 
                    class="px-3 py-1 rounded border border-gray-600 text-gray-300 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                下一页
            </button>
        `;
        
        container.innerHTML = html;
    }

    getParkingName(parkingLotId) {
        const parkingLots = storage.getParkingLots();
        const parking = parkingLots.find(p => p.id === parkingLotId);
        return parking ? parking.name : '未知停车场';
    }

    calculateDuration(entryTime, exitTime) {
        if (!entryTime || !exitTime) return '--';
        
        const entry = new Date(entryTime);
        const exit = new Date(exitTime);
        const durationMs = exit - entry;
        
        const hours = Math.floor(durationMs / (1000 * 60 * 60));
        const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
        
        if (hours > 0) {
            return `${hours}小时${minutes}分`;
        }
        return `${minutes}分钟`;
    }

    showDetail(transactionId) {
        const transactions = storage.getTransactions();
        const transaction = transactions.find(t => t.id === transactionId);
        
        if (!transaction) return;
        
        const modal = document.getElementById('detailModal');
        const details = document.getElementById('transactionDetails');
        
        const parking = this.getParkingName(transaction.parkingLotId);
        const entryTime = new Date(transaction.entryTime);
        const exitTime = new Date(transaction.exitTime);
        const transTime = new Date(transaction.createdAt);
        const duration = this.calculateDuration(transaction.entryTime, transaction.exitTime);
        
        details.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-300 mb-2">基本信息</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-400">车牌号码</span>
                            <span class="font-mono">${transaction.licensePlate}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">停车场</span>
                            <span>${parking}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">交易时间</span>
                            <span>${transTime.toLocaleString('zh-CN')}</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-sm font-medium text-gray-300 mb-2">停车信息</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-400">入场时间</span>
                            <span>${entryTime.toLocaleString('zh-CN')}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">出场时间</span>
                            <span>${exitTime.toLocaleString('zh-CN')}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">停车时长</span>
                            <span>${duration}</span>
                        </div>
                    </div>
                </div>
                
                <div class="md:col-span-2">
                    <h4 class="text-sm font-medium text-gray-300 mb-2">费用详情</h4>
                    <div class="bg-gray-800/50 rounded-lg p-4">
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-semibold">总计</span>
                            <span class="text-2xl font-bold text-green-400">¥${transaction.amount.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        modal.classList.remove('hidden');
        modal.classList.add('flex');
    }

    closeDetailModal() {
        const modal = document.getElementById('detailModal');
        modal.classList.add('hidden');
        modal.classList.remove('flex');
    }

    applyFilters() {
        this.currentPage = 1;
        this.loadRecords();
        componentLoader.showToast('筛选条件已应用', 'success');
    }

    search() {
        this.applyFilters();
    }

    previousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.loadRecords();
        }
    }

    nextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.loadRecords();
        }
    }

    goToPage(page) {
        this.currentPage = page;
        this.loadRecords();
    }

    exportData() {
        const data = storage.exportData();
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `parking_data_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        componentLoader.showToast('数据导出成功', 'success');
    }

    setupEventListeners() {
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeDetailModal();
            }
        });

        // 点击模态框背景关闭
        document.getElementById('detailModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closeDetailModal();
            }
        });

        // 输入框实时搜索
        document.getElementById('searchInput').addEventListener('input', () => {
            this.applyFilters();
        });

        // 监听停车场切换事件
        window.addEventListener('parkingLotChanged', () => {
            this.loadParkingFilter();
            this.loadStats();
            this.loadRecords();
        });
    }

    checkUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        const searchTerm = urlParams.get('search');
        
        if (searchTerm) {
            document.getElementById('searchInput').value = searchTerm;
            this.applyFilters();
        }
    }
}

// 全局实例
const billingRecords = new BillingRecords();

// 全局函数供HTML调用
function closeDetailModal() {
    billingRecords.closeDetailModal();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 组件加载完成后初始化
    setTimeout(() => {
        billingRecords.loadParkingFilter();
    }, 100);
});