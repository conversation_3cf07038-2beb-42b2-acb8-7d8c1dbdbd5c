// 工具函数：格式化时间
function formatDateTime(date) {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(new Date(date))
}

class Dashboard {
    constructor() {
        this.init();
    }

    init() {
        this.loadData();
        this.setupEventListeners();
        this.startRealTimeUpdates();
    }

    loadData() {
        this.updateDashboardStats();
        this.renderParkingOverview();
        this.renderActivityStream();
        this.animateNumbers();
    }

    updateDashboardStats() {
        const parkingLots = storage.getParkingLots();
        const todayStats = storage.getTodayStats();
        const currentParking = storage.getCurrentParkingLot();
        
        // 更新停车场数量
        document.getElementById('parkingCount').textContent = parkingLots.length;
        
        // 更新今日车流量（当前停车场）
        const currentParkingStats = currentParking ? storage.getParkingLotStats(currentParking.id) : null;
        const currentVehicleCount = currentParkingStats ? currentParkingStats.occupiedSpaces : 0;
        document.getElementById('todayTraffic').textContent = currentVehicleCount;
        
        // 更新当前收入（当前停车场）
        const currentParkingTransactions = storage.getCurrentParkingLotTransactions();
        const today = new Date().toDateString();
        const todayCurrentRevenue = currentParkingTransactions
            .filter(t => new Date(t.createdAt).toDateString() === today)
            .reduce((sum, t) => sum + t.amount, 0);
        document.getElementById('todayRevenue').textContent = `¥${todayCurrentRevenue.toFixed(2)}`;
        
        // 计算并更新车位使用率（当前停车场）
        let usageRate = 0;
        if (currentParkingStats) {
            usageRate = Math.round((currentParkingStats.occupiedSpaces / currentParkingStats.totalSpaces) * 100);
        }
        document.getElementById('usageRate').textContent = `${usageRate}%`;
        
        // 更新进度条
        const progressBars = document.querySelectorAll('.bg-blue-500, .bg-red-500');
        progressBars.forEach(bar => {
            setTimeout(() => {
                bar.style.width = `${usageRate}%`;
            }, 100);
        });
    }

    renderParkingOverview() {
        const container = document.getElementById('parkingOverview');
        const parkingLots = storage.getParkingLots();
        
        if (parkingLots.length === 0) {
            container.innerHTML = `
                <div class="col-span-full text-center py-8">
                    <div class="text-gray-400 text-6xl mb-4">🅿️</div>
                    <h3 class="text-gray-300 text-lg mb-2">暂无停车场</h3>
                    <p class="text-gray-500 text-sm">请先添加停车场信息</p>
                    <button onclick="location.href='parking-management.html'" 
                            class="mt-4 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
                        添加停车场
                    </button>
                </div>
            `;
            return;
        }
        
        let html = '';
        parkingLots.forEach(parking => {
            const stats = storage.getParkingLotStats(parking.id);
            const usageRate = stats ? stats.usageRate : 0;
            const statusColor = this.getStatusColor(usageRate);
            
            html += `
                <div class="glass-card p-4 rounded-xl hover:scale-105 transition-transform duration-200 cursor-pointer"
                     onclick="location.href='parking-status.html?parking=${parking.id}'">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-white">${parking.name}</h3>
                        <span class="text-xs px-2 py-1 rounded-full ${statusColor}">
                            ${usageRate}% 使用率
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <div class="flex justify-between text-sm text-gray-300 mb-1">
                            <span>总车位</span>
                            <span>${stats ? stats.totalSpaces : 0}</span>
                        </div>
                        <div class="flex justify-between text-sm text-gray-300 mb-1">
                            <span>已占用</span>
                            <span class="text-red-400">${stats ? stats.occupiedSpaces : 0}</span>
                        </div>
                        <div class="flex justify-between text-sm text-gray-300">
                            <span>空闲</span>
                            <span class="text-green-400">${stats ? stats.availableSpaces : 0}</span>
                        </div>
                    </div>
                    
                    <div class="w-full bg-gray-700 rounded-full h-2 mb-2">
                        <div class="bg-gradient-to-r from-green-400 via-yellow-400 to-red-400 h-2 rounded-full transition-all duration-1000" 
                             style="width: ${usageRate}%"></div>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-xs text-gray-400">
                            ${parking.location || '未设置位置'}
                        </span>
                        <button class="text-blue-400 hover:text-blue-300 text-xs" 
                                onclick="event.stopPropagation(); location.href='parking-management.html?edit=${parking.id}'">
                            管理
                        </button>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    renderActivityStream() {
        const container = document.getElementById('activityStream');
        const transactions = storage.getCurrentParkingLotTransactions().slice(0, 10); // 当前停车场最新10条记录
        const vehicles = storage.getCurrentParkingLotVehicles().slice(0, 5); // 当前停车场最新5辆车辆
        
        if (transactions.length === 0 && vehicles.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <div class="text-4xl mb-2">📋</div>
                    <p>暂无活动记录</p>
                </div>
            `;
            return;
        }
        
        let html = '';
        
        // 添加交易记录
        transactions.forEach(transaction => {
            const time = formatDateTime(transaction.createdAt);
            html += `
                <div class="flex items-start space-x-3 p-3 bg-gray-800/50 rounded-lg">
                    <div class="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-green-400 text-sm">💰</span>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-white">
                            车牌 <span class="font-mono">${transaction.licensePlate}</span> 完成收费
                        </p>
                        <p class="text-xs text-gray-400">
                            金额: <span class="text-green-400">¥${transaction.amount.toFixed(2)}</span> • ${time}
                        </p>
                    </div>
                </div>
            `;
        });
        
        // 添加车辆记录
        vehicles.forEach(vehicle => {
            const time = formatDateTime(vehicle.entryTime);
            const status = vehicle.status === 'parked' ? '入场' : '出场';
            const statusColor = vehicle.status === 'parked' ? 'text-blue-400' : 'text-gray-400';
            const icon = vehicle.status === 'parked' ? '🚗' : '🚪';
            
            html += `
                <div class="flex items-start space-x-3 p-3 bg-gray-800/50 rounded-lg">
                    <div class="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                        <span class="text-blue-400 text-sm">${icon}</span>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-white">
                            车牌 <span class="font-mono">${vehicle.licensePlate}</span> <span class="${statusColor}">${status}</span>
                        </p>
                        <p class="text-xs text-gray-400">
                            停车场: ${this.getParkingName(vehicle.parkingLotId)} • ${time}
                        </p>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    getParkingName(parkingLotId) {
        const parkingLots = storage.getParkingLots();
        const parking = parkingLots.find(p => p.id === parkingLotId);
        return parking ? parking.name : '未知停车场';
    }

    getStatusColor(usageRate) {
        if (usageRate >= 80) return 'bg-red-500/20 text-red-400';
        if (usageRate >= 50) return 'bg-yellow-500/20 text-yellow-400';
        return 'bg-green-500/20 text-green-400';
    }

    animateNumbers() {
        // 数字滚动动画
        const counters = document.querySelectorAll('.count-up');
        counters.forEach(counter => {
            const target = parseInt(counter.textContent.replace(/[^0-9]/g, '') || 0);
            animateNumber(counter, target, 1500);
        });
    }

    setupEventListeners() {
        // 快捷操作按钮
        const quickActions = document.querySelectorAll('.glass-card.cursor-pointer');
        quickActions.forEach(action => {
            action.addEventListener('click', function() {
                const text = this.querySelector('h3').textContent;
                if (text.includes('入场')) {
                    location.href = 'vehicle-entry.html';
                } else if (text.includes('出场')) {
                    location.href = 'vehicle-entry.html?action=exit';
                } else if (text.includes('查询')) {
                    // 实现搜索功能
                    const searchTerm = prompt('请输入车牌号:');
                    if (searchTerm) {
                        location.href = `billing-records.html?search=${encodeURIComponent(searchTerm)}`;
                    }
                }
            });
        });

        // 监听停车场切换事件
        window.addEventListener('parkingLotChanged', () => {
            this.loadData();
        });
    }

    startRealTimeUpdates() {
        // 每30秒更新一次数据
        setInterval(() => {
            this.updateDashboardStats();
            this.renderActivityStream();
        }, 30000);
    }
}

// 页面加载完成后初始化Dashboard
document.addEventListener('DOMContentLoaded', function() {
    new Dashboard();
    
    // 添加一些示例数据（如果没有数据）
    const parkingLots = storage.getParkingLots();
    if (parkingLots.length === 0) {
        // 添加示例停车场
        storage.addParkingLot({
            name: 'A区地下停车场',
            location: 'B1层',
            totalSpaces: 50,
            hourlyRate: 5
        });
        
        storage.addParkingLot({
            name: 'B区地面停车场', 
            location: '广场东侧',
            totalSpaces: 30,
            hourlyRate: 3
        });
        
        componentLoader.showToast('已加载示例数据', 'info');
    }
});