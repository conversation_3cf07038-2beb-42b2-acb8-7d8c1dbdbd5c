<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车场数据测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .data-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .data-section h3 { margin-top: 0; color: #333; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>停车场数据测试页面</h1>
    
    <div class="data-section">
        <h3>LocalStorage 原始数据</h3>
        <button onclick="showLocalStorageData()">显示 LocalStorage 数据</button>
        <pre id="localStorage-data">点击按钮查看数据</pre>
    </div>
    
    <div class="data-section">
        <h3>Storage 对象数据</h3>
        <button onclick="showStorageData()">显示 Storage 对象数据</button>
        <pre id="storage-data">点击按钮查看数据</pre>
    </div>
    
    <div class="data-section">
        <h3>当前停车场信息</h3>
        <button onclick="showCurrentParking()">显示当前停车场</button>
        <pre id="current-parking">点击按钮查看数据</pre>
    </div>
    
    <div class="data-section">
        <h3>操作测试</h3>
        <button onclick="addTestParking()">添加测试停车场</button>
        <button onclick="clearAllData()">清空所有数据</button>
        <button onclick="reloadPage()">重新加载页面</button>
    </div>

    <script src="js/storage.js"></script>
    <script>
        function showLocalStorageData() {
            const data = {
                parkingLots: JSON.parse(localStorage.getItem('parkingLots') || '[]'),
                currentParkingLotId: localStorage.getItem('currentParkingLotId'),
                vehicles: JSON.parse(localStorage.getItem('vehicles') || '[]'),
                transactions: JSON.parse(localStorage.getItem('transactions') || '[]')
            };
            document.getElementById('localStorage-data').textContent = JSON.stringify(data, null, 2);
        }
        
        function showStorageData() {
            if (typeof storage === 'undefined') {
                document.getElementById('storage-data').textContent = 'Storage 对象未定义';
                return;
            }
            
            const data = {
                parkingLots: storage.getParkingLots(),
                currentParkingLotId: storage.getCurrentParkingLotId(),
                currentParkingLot: storage.getCurrentParkingLot(),
                vehicles: storage.getVehicles(),
                transactions: storage.getTransactions()
            };
            document.getElementById('storage-data').textContent = JSON.stringify(data, null, 2);
        }
        
        function showCurrentParking() {
            if (typeof storage === 'undefined') {
                document.getElementById('current-parking').textContent = 'Storage 对象未定义';
                return;
            }
            
            const currentParking = storage.getCurrentParkingLot();
            const currentId = storage.getCurrentParkingLotId();
            const allParkings = storage.getParkingLots();
            
            const data = {
                currentParkingLotId: currentId,
                currentParkingLot: currentParking,
                allParkingLots: allParkings,
                totalParkingLots: allParkings.length
            };
            
            document.getElementById('current-parking').textContent = JSON.stringify(data, null, 2);
        }
        
        function addTestParking() {
            if (typeof storage === 'undefined') {
                alert('Storage 对象未定义');
                return;
            }
            
            const testParking = {
                name: '测试停车场 ' + Date.now(),
                location: '测试位置',
                totalSpaces: 20,
                hourlyRate: 5
            };
            
            storage.addParkingLot(testParking);
            alert('已添加测试停车场');
            showStorageData();
            showCurrentParking();
        }
        
        function clearAllData() {
            localStorage.clear();
            alert('已清空所有数据');
            showLocalStorageData();
            showStorageData();
            showCurrentParking();
        }
        
        function reloadPage() {
            location.reload();
        }
        
        // 页面加载时自动显示数据
        window.addEventListener('load', function() {
            setTimeout(() => {
                showLocalStorageData();
                showStorageData();
                showCurrentParking();
            }, 500);
        });
    </script>
</body>
</html>
