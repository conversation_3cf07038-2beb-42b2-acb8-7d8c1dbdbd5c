<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>停车场管理系统 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      // 配置Tailwind CSS生产环境
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {},
        },
      }
    </script>
    <link rel="stylesheet" href="css/style.css" />
  </head>
  <body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div id="header">
      <!-- Header content will be inserted here -->
    </div>
    <div id="sidebar">
      <!-- Sidebar content will be inserted here -->
    </div>

    <main class="ml-0 lg:ml-64 pt-16 p-6">
      <div class="max-w-7xl mx-auto">
        <!-- 实时数据看板 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div class="glass-card p-6 rounded-2xl">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-300 text-sm">停车场数量</p>
                <h2 class="text-3xl font-bold count-up" id="parkingCount">0</h2>
              </div>
              <div class="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center">
                🅿️
              </div>
            </div>
            <div class="mt-2">
              <div class="w-full bg-gray-700 rounded-full h-2">
                <div
                  class="bg-blue-500 h-2 rounded-full transition-all duration-1000"
                  style="width: 0%"
                ></div>
              </div>
            </div>
          </div>

          <div class="glass-card p-6 rounded-2xl">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-300 text-sm">今日车流量</p>
                <h2 class="text-3xl font-bold count-up" id="todayTraffic">0</h2>
              </div>
              <div class="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center">
                🚗
              </div>
            </div>
            <div class="mt-2 flex items-center">
              <span class="text-green-400 text-sm">+12%</span>
              <span class="text-gray-400 text-sm ml-2">较昨日</span>
            </div>
          </div>

          <div class="glass-card p-6 rounded-2xl">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-300 text-sm">当前收入</p>
                <h2 class="text-3xl font-bold count-up" id="todayRevenue">0</h2>
              </div>
              <div class="w-12 h-12 bg-yellow-500/20 rounded-full flex items-center justify-center">
                💰
              </div>
            </div>
            <div class="mt-2 flex items-center">
              <span class="text-yellow-400 text-sm">¥0</span>
              <span class="text-gray-400 text-sm ml-2">今日累计</span>
            </div>
          </div>

          <div class="glass-card p-6 rounded-2xl">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-gray-300 text-sm">车位使用率</p>
                <h2 class="text-3xl font-bold count-up" id="usageRate">0%</h2>
              </div>
              <div class="w-12 h-12 bg-red-500/20 rounded-full flex items-center justify-center">
                📊
              </div>
            </div>
            <div class="mt-2">
              <div class="w-full bg-gray-700 rounded-full h-2">
                <div
                  class="bg-red-500 h-2 rounded-full transition-all duration-1000"
                  style="width: 0%"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 快捷操作面板 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div
            class="glass-card p-6 rounded-2xl text-center cursor-pointer hover:scale-105 transition-transform duration-200"
          >
            <div
              class="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <span class="text-2xl">🚗</span>
            </div>
            <h3 class="text-lg font-semibold mb-2">车辆入场</h3>
            <p class="text-gray-300 text-sm">记录新车辆进入停车场</p>
          </div>

          <div
            class="glass-card p-6 rounded-2xl text-center cursor-pointer hover:scale-105 transition-transform duration-200"
          >
            <div
              class="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <span class="text-2xl">🚪</span>
            </div>
            <h3 class="text-lg font-semibold mb-2">车辆出场</h3>
            <p class="text-gray-300 text-sm">处理车辆离开并计算费用</p>
          </div>

          <div
            class="glass-card p-6 rounded-2xl text-center cursor-pointer hover:scale-105 transition-transform duration-200"
          >
            <div
              class="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <span class="text-2xl">🔍</span>
            </div>
            <h3 class="text-lg font-semibold mb-2">车牌查询</h3>
            <p class="text-gray-300 text-sm">快速搜索车辆记录</p>
          </div>
        </div>

        <!-- 停车场概览 -->
        <div class="glass-card p-6 rounded-2xl mb-8">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-semibold">停车场概览</h2>
            <button class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors">
              管理停车场
            </button>
          </div>
          <div id="parkingOverview" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- 停车场卡片将通过JS动态生成 -->
          </div>
        </div>

        <!-- 实时活动流 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div class="glass-card p-6 rounded-2xl">
            <h2 class="text-xl font-semibold mb-4">实时活动</h2>
            <div id="activityStream" class="space-y-3">
              <!-- 活动记录将通过JS动态生成 -->
            </div>
          </div>

          <div class="glass-card p-6 rounded-2xl">
            <h2 class="text-xl font-semibold mb-4">统计图表</h2>
            <div class="bg-gray-800/50 rounded-lg p-4 h-64 flex items-center justify-center">
              <p class="text-gray-400">24小时车流量图表区域</p>
            </div>
          </div>
        </div>
      </div>
    </main>

    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="debug-parking.js"></script>
    <script src="diagnose-dropdown.js"></script>
    <script src="test-z-index-fix.js"></script>
  </body>
</html>
