<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 智慧停车场系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div id="header"></div>
    <div id="sidebar"></div>
    
    <main class="ml-0 lg:ml-64 pt-16 p-6">
        <div class="max-w-4xl mx-auto">
            <div class="mb-8">
                <h1 class="text-2xl font-bold">系统设置</h1>
                <p class="text-gray-400">管理系统配置和参数设置</p>
            </div>

            <!-- 费率设置 -->
            <div class="glass-card p-6 rounded-2xl mb-8">
                <h2 class="text-xl font-semibold mb-6">计费设置</h2>
                <form id="billingSettings" class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">默认小时费率 (元)</label>
                        <input type="number" name="hourlyRate" required min="0" step="0.5"
                               class="input-glass w-full" 
                               placeholder="例如：5.0">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">免费停车时长 (分钟)</label>
                        <input type="number" name="freeMinutes" required min="0" max="120"
                               class="input-glass w-full" 
                               placeholder="例如：15">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">货币符号</label>
                        <select name="currency" class="input-glass w-full">
                            <option value="¥">人民币 (¥)</option>
                            <option value="$">美元 ($)</option>
                            <option value="€">欧元 (€)</option>
                            <option value="£">英镑 (£)</option>
                        </select>
                    </div>

                    <div class="pt-4">
                        <button type="submit" class="btn-primary w-full">保存设置</button>
                    </div>
                </form>
            </div>

            <!-- 数据管理 -->
            <div class="glass-card p-6 rounded-2xl mb-8">
                <h2 class="text-xl font-semibold mb-6">数据管理</h2>
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-4 bg-gray-800/50 rounded-lg">
                        <div>
                            <h3 class="font-semibold">导出所有数据</h3>
                            <p class="text-sm text-gray-400">导出停车场数据为JSON文件</p>
                        </div>
                        <button onclick="exportAllData()" class="btn-secondary">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                            </svg>
                            导出数据
                        </button>
                    </div>

                    <div class="flex justify-between items-center p-4 bg-gray-800/50 rounded-lg">
                        <div>
                            <h3 class="font-semibold">导入数据</h3>
                            <p class="text-sm text-gray-400">从JSON文件导入数据</p>
                        </div>
                        <div>
                            <input type="file" id="importFile" accept=".json" class="hidden">
                            <button onclick="document.getElementById('importFile').click()" class="btn-secondary">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                </svg>
                                选择文件
                            </button>
                        </div>
                    </div>

                    <div class="flex justify-between items-center p-4 bg-red-500/10 rounded-lg">
                        <div>
                            <h3 class="font-semibold text-red-400">清空所有数据</h3>
                            <p class="text-sm text-red-300">删除所有停车场和交易记录</p>
                        </div>
                        <button onclick="confirmClearData()" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg transition-colors">
                            清空数据
                        </button>
                    </div>
                </div>
            </div>

            <!-- 系统信息 -->
            <div class="glass-card p-6 rounded-2xl">
                <h2 class="text-xl font-semibold mb-6">系统信息</h2>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-300">版本号</span>
                        <span class="font-mono">v1.0.0</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-300">数据存储</span>
                        <span class="font-mono" id="storageUsage">计算中...</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-300">最后更新</span>
                        <span class="font-mono">${new Date().toLocaleDateString('zh-CN')}</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script>
    // 加载当前设置
    document.addEventListener('DOMContentLoaded', function() {
        loadCurrentSettings();
        calculateStorageUsage();
        
        // 文件导入处理
        document.getElementById('importFile').addEventListener('change', handleFileImport);
        
        // 设置表单提交
        document.getElementById('billingSettings').addEventListener('submit', saveSettings);
    });

    function loadCurrentSettings() {
        const settings = storage.getSettings();
        const form = document.getElementById('billingSettings');
        
        form.hourlyRate.value = settings.hourlyRate || 5;
        form.freeMinutes.value = settings.freeMinutes || 15;
        form.currency.value = settings.currency || '¥';
    }

    function saveSettings(e) {
        e.preventDefault();
        
        const form = document.getElementById('billingSettings');
        const settings = {
            hourlyRate: parseFloat(form.hourlyRate.value),
            freeMinutes: parseInt(form.freeMinutes.value),
            currency: form.currency.value
        };
        
        storage.updateSettings(settings);
        componentLoader.showToast('设置保存成功', 'success');
    }

    function exportAllData() {
        const data = storage.exportData();
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `parking_system_backup_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        componentLoader.showToast('数据导出成功', 'success');
    }

    function handleFileImport(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);
                
                if (confirm('确定要导入数据吗？这将覆盖当前所有数据。')) {
                    storage.importData(data);
                    componentLoader.showToast('数据导入成功', 'success');
                    loadCurrentSettings();
                }
            } catch (error) {
                componentLoader.showToast('文件格式错误', 'error');
            }
        };
        reader.readAsText(file);
    }

    function confirmClearData() {
        if (confirm('⚠️  确定要清空所有数据吗？此操作不可恢复！')) {
            localStorage.clear();
            storage.initializeStorage();
            componentLoader.showToast('数据已清空', 'success');
            loadCurrentSettings();
        }
    }

    function calculateStorageUsage() {
        let totalSize = 0;
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                totalSize += localStorage[key].length * 2; // UTF-16 字符占2字节
            }
        }
        
        const sizeInKB = (totalSize / 1024).toFixed(2);
        document.getElementById('storageUsage').textContent = `${sizeInKB} KB`;
    }
    </script>
</body>
</html>