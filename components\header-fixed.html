<header
  class="fixed top-0 left-0 right-0 bg-gray-900/80 backdrop-blur-md border-b border-gray-700 z-50"
>
  <div class="flex items-center justify-between px-6 py-4">
    <div class="flex items-center space-x-4">
      <button id="sidebarToggle" class="lg:hidden text-gray-300 hover:text-white">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 6h16M4 12h16M4 18h16"
          ></path>
        </svg>
      </button>
      <h1 class="text-xl font-bold text-white">
        <span class="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
          智慧停车场管理系统
        </span>
      </h1>
    </div>

    <div class="flex items-center space-x-4">
      <!-- 停车场切换器 -->
      <div class="relative" id="parkingSwitcher">
        <button
          class="flex items-center space-x-2 px-3 py-2 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors"
        >
          <span class="text-sm text-gray-300" id="currentParkingName">选择停车场</span>
          <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 9l-7 7-7-7"
            ></path>
          </svg>
        </button>

        <div
          class="absolute right-0 mt-2 w-64 bg-gray-800 rounded-lg shadow-lg opacity-0 invisible transition-all duration-200 transform scale-95"
          id="parkingDropdown"
          style="z-index: 9999"
        >
          <div class="py-2 max-h-60 overflow-y-auto" id="parkingListDropdown">
            <!-- 停车场列表将通过JS动态生成 -->
          </div>
        </div>
      </div>

      <div class="hidden md:flex items-center space-x-2 text-sm text-gray-300">
        <div class="status-indicator status-active"></div>
        <span>系统运行中</span>
      </div>

      <div class="relative">
        <button
          class="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-gray-600 transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
            ></path>
          </svg>
        </button>

        <div
          class="absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg opacity-0 invisible transition-all duration-200 transform scale-95"
        >
          <div class="py-2">
            <a href="#" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">个人资料</a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">系统设置</a>
            <div class="border-t border-gray-700 my-1"></div>
            <a href="#" class="block px-4 py-2 text-sm text-red-400 hover:bg-gray-700">退出登录</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>

<script>
  class ParkingSwitcher {
    constructor() {
      this.init()
    }

    init() {
      this.loadCurrentParking()
      this.renderParkingList()
      this.setupEventListeners()
    }

    loadCurrentParking() {
      const currentParking = storage.getCurrentParkingLot()
      const nameElement = document.getElementById('currentParkingName')

      if (currentParking) {
        const stats = storage.getParkingLotStats(currentParking.id)
        const statusText = stats ? stats.availableSpaces + '空位' : '加载中'
        nameElement.textContent = currentParking.name + ' (' + statusText + ')'
        nameElement.classList.remove('text-gray-300')
        nameElement.classList.add('text-white', 'font-medium')
      } else {
        nameElement.textContent = '选择停车场'
        nameElement.classList.remove('text-white', 'font-medium')
        nameElement.classList.add('text-gray-300')
      }
    }

    renderParkingList() {
      const container = document.getElementById('parkingListDropdown')
      const parkingLots = storage.getParkingLots()

      if (parkingLots.length === 0) {
        container.innerHTML =
          '<div class="px-4 py-3 text-sm text-gray-400 text-center">暂无停车场</div>'
        return
      }

      let html = ''
      const currentId = storage.getCurrentParkingLotId()

      parkingLots.forEach(parking => {
        const stats = storage.getParkingLotStats(parking.id)
        const isCurrent = parking.id === currentId
        const statusColor = this.getStatusColor(stats ? stats.usageRate : 0)

        const availableText = stats
          ? stats.availableSpaces + '/' + stats.totalSpaces + '空位'
          : '加载中'
        const checkmark = isCurrent
          ? '<span class="text-blue-400">✓</span>'
          : '<span class="w-4"></span>'
        const nameClass = isCurrent ? 'text-white font-medium' : 'text-gray-300'
        const usageRateText = stats ? stats.usageRate + '%' : '0%'

        html +=
          '<button class="w-full text-left px-4 py-3 hover:bg-gray-700 transition-colors ' +
          (isCurrent ? 'bg-blue-600/20' : '') +
          '" onclick="parkingSwitcher.switchParking(\'' +
          parking.id.replace(/'/g, "\\'") +
          '\')" data-parking-id="' +
          parking.id.replace(/"/g, '&quot;') +
          '">'
        html += '<div class="flex items-center justify-between">'
        html += '<div class="flex items-center space-x-3">'
        html += checkmark
        html += '<span class="text-sm ' + nameClass + '">' + parking.name + '</span>'
        html += '</div>'
        html +=
          '<span class="text-xs px-2 py-1 rounded-full ' +
          statusColor +
          '">' +
          usageRateText +
          '</span>'
        html += '</div>'
        html += '<div class="text-xs text-gray-400 mt-1 ml-7">'
        html += (parking.location || '未设置位置') + ' • ' + availableText
        html += '</div>'
        html += '</button>'
      })

      container.innerHTML = html
    }

    getStatusColor(usageRate) {
      if (usageRate >= 80) return 'bg-red-500/20 text-red-400'
      if (usageRate >= 50) return 'bg-yellow-500/20 text-yellow-400'
      return 'bg-green-500/20 text-green-400'
    }

    switchParking(parkingLotId) {
      storage.setCurrentParkingLotId(parkingLotId)
      this.loadCurrentParking()
      this.renderParkingList()

      const parking = storage.getParkingLots().find(p => p.id === parkingLotId)
      if (parking) {
        componentLoader.showToast('已切换到: ' + parking.name, 'success')
      }
    }

    setupEventListeners() {
      window.addEventListener('parkingLotChanged', event => {
        this.loadCurrentParking()
        this.renderParkingList()
      })

      window.addEventListener('storage', event => {
        if (event.key === 'parkingLots' || event.key === 'currentParkingLotId') {
          this.loadCurrentParking()
          this.renderParkingList()
        }
      })
    }
  }

  document.addEventListener('DOMContentLoaded', function () {
    // 初始化停车场切换器
    if (typeof storage !== 'undefined' && typeof componentLoader !== 'undefined') {
      window.parkingSwitcher = new ParkingSwitcher()
    }

    const profileButton = document.querySelector('header .relative:last-child button')
    const dropdown = document.querySelector('header .relative:last-child .absolute')

    profileButton.addEventListener('click', function () {
      dropdown.classList.toggle('opacity-0')
      dropdown.classList.toggle('invisible')
      dropdown.classList.toggle('scale-95')
      dropdown.classList.toggle('opacity-100')
      dropdown.classList.toggle('visible')
      dropdown.classList.toggle('scale-100')
    })

    // 点击外部关闭下拉菜单
    document.addEventListener('click', function (event) {
      if (!profileButton.contains(event.target) && !dropdown.contains(event.target)) {
        dropdown.classList.add('opacity-0', 'invisible', 'scale-95')
        dropdown.classList.remove('opacity-100', 'visible', 'scale-100')
      }
    })
  })
</script>
