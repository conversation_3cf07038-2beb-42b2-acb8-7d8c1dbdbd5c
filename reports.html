<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计报表 - 智慧停车场系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div id="header"></div>
    <div id="sidebar"></div>
    
    <main class="ml-0 lg:ml-64 pt-16 p-6">
        <div class="max-w-7xl mx-auto">
            <div class="mb-8">
                <h1 class="text-2xl font-bold">统计报表</h1>
                <p class="text-gray-400">查看停车场运营数据和分析报告</p>
            </div>

            <!-- 报表选择器 -->
            <div class="glass-card p-6 rounded-2xl mb-8">
                <h2 class="text-xl font-semibold mb-4">选择报表类型</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <button onclick="showReport('daily')" class="btn-secondary text-center py-4">
                        <div class="text-2xl mb-2">📅</div>
                        <div>日报表</div>
                    </button>
                    <button onclick="showReport('weekly')" class="btn-secondary text-center py-4">
                        <div class="text-2xl mb-2">📆</div>
                        <div>周报表</div>
                    </button>
                    <button onclick="showReport('monthly')" class="btn-secondary text-center py-4">
                        <div class="text-2xl mb-2">📊</div>
                        <div>月报表</div>
                    </button>
                    <button onclick="showReport('parking')" class="btn-secondary text-center py-4">
                        <div class="text-2xl mb-2">🅿️</div>
                        <div>停车场对比</div>
                    </button>
                </div>
            </div>

            <!-- 报表内容区域 -->
            <div id="reportContent" class="glass-card p-6 rounded-2xl">
                <div class="text-center py-12 text-gray-500">
                    <div class="text-4xl mb-4">📈</div>
                    <p>请选择要查看的报表类型</p>
                </div>
            </div>
        </div>
    </main>

    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script>
    function showReport(type) {
        const container = document.getElementById('reportContent');
        
        switch (type) {
            case 'daily':
                container.innerHTML = `
                    <h2 class="text-xl font-semibold mb-6">今日运营报表</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-gray-800/50 rounded-lg p-4 h-64 flex items-center justify-center">
                            <p class="text-gray-400">24小时车流量图表</p>
                        </div>
                        <div class="bg-gray-800/50 rounded-lg p-4 h-64 flex items-center justify-center">
                            <p class="text-gray-400">收入趋势图表</p>
                        </div>
                    </div>
                    <div class="bg-gray-800/50 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-4">今日数据摘要</h3>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-400">0</div>
                                <div class="text-sm text-gray-400">总车流量</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-400">¥0.00</div>
                                <div class="text-sm text-gray-400">总收入</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-yellow-400">0</div>
                                <div class="text-sm text-gray-400">平均停车时长</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-400">0%</div>
                                <div class="text-sm text-gray-400">车位使用率</div>
                            </div>
                        </div>
                    </div>
                `;
                break;
            
            case 'weekly':
                container.innerHTML = `
                    <h2 class="text-xl font-semibold mb-6">本周运营报表</h2>
                    <div class="bg-gray-800/50 rounded-lg p-4 h-96 flex items-center justify-center mb-6">
                        <p class="text-gray-400">周度数据对比图表</p>
                    </div>
                `;
                break;
                
            case 'monthly':
                container.innerHTML = `
                    <h2 class="text-xl font-semibold mb-6">月度运营报表</h2>
                    <div class="bg-gray-800/50 rounded-lg p-4 h-96 flex items-center justify-center mb-6">
                        <p class="text-gray-400">月度趋势分析图表</p>
                    </div>
                `;
                break;
                
            case 'parking':
                container.innerHTML = `
                    <h2 class="text-xl font-semibold mb-6">停车场对比报表</h2>
                    <div class="bg-gray-800/50 rounded-lg p-4 h-96 flex items-center justify-center">
                        <p class="text-gray-400">停车场性能对比图表</p>
                    </div>
                `;
                break;
        }
    }
    </script>
</body>
</html>