class ParkingStatus {
    constructor() {
        this.currentParkingId = null;
        this.init();
    }

    init() {
        this.loadParkingSelector();
        this.setupEventListeners();
        this.checkUrlParams();
        this.startAutoRefresh();
    }

    loadParkingSelector() {
        const container = document.getElementById('parkingSelectorContainer');
        const parkingLots = storage.getParkingLots();
        
        if (parkingLots.length === 0) {
            container.innerHTML = `
                <div class="text-red-400 text-sm">
                    暂无停车场，请先<a href="parking-management.html" class="underline">添加停车场</a>
                </div>
            `;
            return;
        }

        let html = '<select class="input-glass" id="parkingSelect" onchange="parkingStatus.selectParking(this.value)">';
        html += '<option value="">选择停车场</option>';
        
        const currentParkingId = storage.getCurrentParkingLotId();
        if (!this.currentParkingId) {
            this.currentParkingId = currentParkingId;
        }
        
        parkingLots.forEach(parking => {
            const stats = storage.getParkingLotStats(parking.id);
            const selected = parking.id === this.currentParkingId ? 'selected' : '';
            const availableText = stats ? ` (${stats.availableSpaces}/${stats.totalSpaces} 空闲)` : '';
            html += `<option value="${parking.id}" ${selected}>${parking.name}${availableText}</option>`;
        });
        
        html += '</select>';
        container.innerHTML = html;

        // 如果URL参数没有指定停车场，使用当前停车场
        if (!this.currentParkingId && currentParkingId) {
            this.selectParking(currentParkingId);
        } else if (!this.currentParkingId && parkingLots.length > 0) {
            this.selectParking(parkingLots[0].id);
        }
    }

    selectParking(parkingId) {
        this.currentParkingId = parkingId;
        // 同步全局当前停车场状态
        storage.setCurrentParkingLotId(parkingId);
        this.loadParkingData();
    }

    loadParkingData() {
        if (!this.currentParkingId) return;

        const parking = storage.getParkingLots().find(p => p.id === this.currentParkingId);
        if (!parking) return;

        const stats = storage.getParkingLotStats(this.currentParkingId);
        
        // 更新标题
        document.getElementById('gridTitle').textContent = `${parking.name} - 车位状态`;
        
        // 更新统计卡片
        this.updateStatsCards(stats, parking);
        
        // 更新车位网格
        this.updateParkingGrid(stats, parking);
        
        // 更新当前车辆列表
        this.updateCurrentVehicles();
    }

    updateStatsCards(stats, parking) {
        const container = document.getElementById('statsCards');
        
        const cards = [
            {
                title: '总车位数',
                value: stats.totalSpaces,
                icon: '🅿️',
                color: 'bg-blue-500/20',
                textColor: 'text-blue-400'
            },
            {
                title: '空闲车位',
                value: stats.availableSpaces,
                icon: '✅',
                color: 'bg-green-500/20',
                textColor: 'text-green-400'
            },
            {
                title: '已占用',
                value: stats.occupiedSpaces,
                icon: '🔴',
                color: 'bg-red-500/20',
                textColor: 'text-red-400'
            },
            {
                title: '使用率',
                value: `${stats.usageRate}%`,
                icon: '📊',
                color: this.getUsageRateColor(stats.usageRate),
                textColor: this.getUsageRateTextColor(stats.usageRate)
            }
        ];

        let html = '';
        cards.forEach(card => {
            html += `
                <div class="glass-card p-4 rounded-xl">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-300 text-sm">${card.title}</p>
                            <h2 class="text-2xl font-bold ${card.textColor}">${card.value}</h2>
                        </div>
                        <div class="w-10 h-10 ${card.color} rounded-full flex items-center justify-center">
                            <span class="text-lg">${card.icon}</span>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    updateParkingGrid(stats, parking) {
        const container = document.getElementById('parkingGrid');
        const vehicles = storage.getVehicles().filter(v => 
            v.parkingLotId === this.currentParkingId && v.status === 'parked'
        );

        let html = '';
        
        // 生成车位网格
        for (let i = 1; i <= stats.totalSpaces; i++) {
            const isOccupied = i <= stats.occupiedSpaces;
            const vehicle = vehicles[i - 1]; // 简单映射，实际应该根据车位号
            
            const spaceClass = isOccupied 
                ? 'bg-red-400/20 border-red-400 hover:bg-red-400/30' 
                : 'bg-green-400/20 border-green-400 hover:bg-green-400/30';
            
            const spaceIcon = isOccupied ? '🔴' : '✅';
            
            html += `
                <div class="aspect-square rounded-lg border-2 ${spaceClass} flex items-center justify-center cursor-pointer transition-colors"
                     onclick="parkingStatus.showSpaceDetails(${i}, ${isOccupied}, '${vehicle ? vehicle.licensePlate : ''}')">
                    <div class="text-center">
                        <div class="text-2xl mb-1">${spaceIcon}</div>
                        <div class="text-sm font-mono">${i.toString().padStart(2, '0')}</div>
                    </div>
                </div>
            `;
        }

        container.innerHTML = html;
    }

    updateCurrentVehicles() {
        const container = document.getElementById('currentVehicles');
        const vehicles = storage.getVehicles().filter(v => 
            v.parkingLotId === this.currentParkingId && v.status === 'parked'
        );

        if (vehicles.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <div class="text-4xl mb-2">🚗</div>
                    <p>当前无停放车辆</p>
                </div>
            `;
            return;
        }

        let html = '';
        vehicles.forEach(vehicle => {
            const entryTime = new Date(vehicle.entryTime);
            const duration = this.formatDuration(new Date() - entryTime);
            
            html += `
                <div class="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
                    <div class="flex items-center space-x-3">
                        <span class="text-2xl">🚗</span>
                        <div>
                            <div class="font-mono text-sm font-semibold">${vehicle.licensePlate}</div>
                            <div class="text-xs text-gray-400">入场: ${entryTime.toLocaleTimeString('zh-CN')}</div>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-green-400">停放中</div>
                        <div class="text-xs text-gray-400">${duration}</div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    showSpaceDetails(spaceNumber, isOccupied, licensePlate) {
        const modal = document.getElementById('spaceModal');
        const title = document.getElementById('modalSpaceTitle');
        const details = document.getElementById('spaceDetails');
        
        title.textContent = `车位 #${spaceNumber.toString().padStart(2, '0')}`;
        
        if (isOccupied) {
            details.innerHTML = `
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <span class="text-red-400 text-2xl">🔴</span>
                        <span class="text-red-400 font-semibold">占用中</span>
                    </div>
                    <div>
                        <p class="text-sm text-gray-300">车牌号码</p>
                        <p class="font-mono text-lg">${licensePlate}</p>
                    </div>
                    <div class="bg-red-400/10 p-3 rounded-lg">
                        <p class="text-sm text-red-300">此车位当前已被占用</p>
                    </div>
                </div>
            `;
        } else {
            details.innerHTML = `
                <div class="space-y-3">
                    <div class="flex items-center space-x-2">
                        <span class="text-green-400 text-2xl">✅</span>
                        <span class="text-green-400 font-semibold">空闲中</span>
                    </div>
                    <div class="bg-green-400/10 p-3 rounded-lg">
                        <p class="text-sm text-green-300">此车位当前可用</p>
                    </div>
                </div>
            `;
        }

        modal.classList.remove('hidden');
        modal.classList.add('flex');
    }

    closeSpaceModal() {
        const modal = document.getElementById('spaceModal');
        modal.classList.add('hidden');
        modal.classList.remove('flex');
    }

    getUsageRateColor(usageRate) {
        if (usageRate >= 80) return 'bg-red-500/20';
        if (usageRate >= 50) return 'bg-yellow-500/20';
        return 'bg-green-500/20';
    }

    getUsageRateTextColor(usageRate) {
        if (usageRate >= 80) return 'text-red-400';
        if (usageRate >= 50) return 'text-yellow-400';
        return 'text-green-400';
    }

    formatDuration(ms) {
        const hours = Math.floor(ms / (1000 * 60 * 60));
        const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
        
        if (hours > 0) {
            return `${hours}小时${minutes}分钟`;
        }
        return `${minutes}分钟`;
    }

    setupEventListeners() {
        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeSpaceModal();
            }
        });

        // 点击模态框背景关闭
        document.getElementById('spaceModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closeSpaceModal();
            }
        });
    }

    checkUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        const parkingId = urlParams.get('parking');
        
        if (parkingId) {
            this.selectParking(parkingId);
        }
    }

    startAutoRefresh() {
        // 每30秒自动刷新数据
        setInterval(() => {
            if (this.currentParkingId) {
                this.loadParkingData();
            }
        }, 30000);
    }

    refreshData() {
        if (this.currentParkingId) {
            this.loadParkingData();
            componentLoader.showToast('数据已刷新', 'success');
        } else {
            componentLoader.showToast('请先选择停车场', 'warning');
        }
    }
}

// 全局实例
const parkingStatus = new ParkingStatus();

// 全局函数供HTML调用
function refreshData() {
    parkingStatus.refreshData();
}

function closeSpaceModal() {
    parkingStatus.closeSpaceModal();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 组件加载完成后初始化
    setTimeout(() => {
        parkingStatus.loadParkingSelector();
    }, 100);
});