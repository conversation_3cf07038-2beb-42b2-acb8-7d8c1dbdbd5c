class ParkingStorage {
  constructor() {
    this.initializeStorage()
  }

  initializeStorage() {
    // 初始化默认数据
    if (!localStorage.getItem('parkingLots')) {
      localStorage.setItem('parkingLots', JSON.stringify([]))
    }
    if (!localStorage.getItem('vehicles')) {
      localStorage.setItem('vehicles', JSON.stringify([]))
    }
    if (!localStorage.getItem('transactions')) {
      localStorage.setItem('transactions', JSON.stringify([]))
    }
    if (!localStorage.getItem('settings')) {
      localStorage.setItem(
        'settings',
        JSON.stringify({
          hourlyRate: 5,
          freeMinutes: 15,
          currency: '¥',
        })
      )
    }
    // 初始化当前停车场状态
    this.initializeCurrentParkingLot()
  }

  initializeCurrentParkingLot() {
    const parkingLots = this.getParkingLots()
    const currentId = localStorage.getItem('currentParkingLotId')

    // 如果没有设置当前停车场ID，或者当前ID对应的停车场不存在
    if (!currentId || !parkingLots.find(p => p.id === currentId)) {
      if (parkingLots.length > 0) {
        console.log('设置默认停车场:', parkingLots[0].name)
        localStorage.setItem('currentParkingLotId', parkingLots[0].id)
      } else {
        localStorage.setItem('currentParkingLotId', '')
      }
    }
  }

  // 停车场管理
  getParkingLots() {
    return JSON.parse(localStorage.getItem('parkingLots') || '[]')
  }

  saveParkingLots(parkingLots) {
    localStorage.setItem('parkingLots', JSON.stringify(parkingLots))
  }

  addParkingLot(parkingLot) {
    const parkingLots = this.getParkingLots()
    parkingLot.id = Date.now().toString()
    parkingLot.createdAt = new Date().toISOString()
    parkingLots.push(parkingLot)
    this.saveParkingLots(parkingLots)

    // 如果这是第一个停车场，设置为当前停车场
    if (parkingLots.length === 1 || !this.getCurrentParkingLotId()) {
      this.setCurrentParkingLotId(parkingLot.id)
    }

    return parkingLot
  }

  updateParkingLot(id, updates) {
    const parkingLots = this.getParkingLots()
    const index = parkingLots.findIndex(p => p.id === id)
    if (index !== -1) {
      parkingLots[index] = { ...parkingLots[index], ...updates }
      this.saveParkingLots(parkingLots)
      return parkingLots[index]
    }
    return null
  }

  deleteParkingLot(id) {
    const parkingLots = this.getParkingLots()
    const filtered = parkingLots.filter(p => p.id !== id)
    this.saveParkingLots(filtered)

    // 如果删除的是当前停车场，切换到第一个停车场
    const currentId = this.getCurrentParkingLotId()
    if (currentId === id && filtered.length > 0) {
      this.setCurrentParkingLotId(filtered[0].id)
    } else if (filtered.length === 0) {
      this.setCurrentParkingLotId('')
    }
  }

  // 当前停车场管理
  getCurrentParkingLotId() {
    return localStorage.getItem('currentParkingLotId') || ''
  }

  setCurrentParkingLotId(parkingLotId) {
    localStorage.setItem('currentParkingLotId', parkingLotId)
    this.dispatchParkingLotChanged(parkingLotId)
  }

  getCurrentParkingLot() {
    const parkingLotId = this.getCurrentParkingLotId()
    if (!parkingLotId) return null

    const parkingLots = this.getParkingLots()
    return parkingLots.find(p => p.id === parkingLotId) || null
  }

  dispatchParkingLotChanged(parkingLotId) {
    // 创建自定义事件通知页面停车场已切换
    const event = new CustomEvent('parkingLotChanged', {
      detail: { parkingLotId },
    })
    window.dispatchEvent(event)
  }

  // 车辆管理
  getVehicles() {
    return JSON.parse(localStorage.getItem('vehicles') || '[]')
  }

  getVehiclesByParkingLot(parkingLotId = null) {
    const vehicles = this.getVehicles()
    if (!parkingLotId) {
      return vehicles
    }
    return vehicles.filter(v => v.parkingLotId === parkingLotId)
  }

  getCurrentParkingLotVehicles() {
    const parkingLotId = this.getCurrentParkingLotId()
    return this.getVehiclesByParkingLot(parkingLotId)
  }

  saveVehicles(vehicles) {
    localStorage.setItem('vehicles', JSON.stringify(vehicles))
  }

  addVehicle(vehicle) {
    const vehicles = this.getVehicles()
    vehicle.id = Date.now().toString()
    vehicle.entryTime = new Date().toISOString()
    vehicle.exitTime = null
    vehicle.status = 'parked'
    vehicles.push(vehicle)
    this.saveVehicles(vehicles)
    return vehicle
  }

  updateVehicle(id, updates) {
    const vehicles = this.getVehicles()
    const index = vehicles.findIndex(v => v.id === id)
    if (index !== -1) {
      vehicles[index] = { ...vehicles[index], ...updates }
      this.saveVehicles(vehicles)
      return vehicles[index]
    }
    return null
  }

  // 交易管理
  getTransactions() {
    return JSON.parse(localStorage.getItem('transactions') || '[]')
  }

  getTransactionsByParkingLot(parkingLotId = null) {
    const transactions = this.getTransactions()
    if (!parkingLotId) {
      return transactions
    }
    return transactions.filter(t => t.parkingLotId === parkingLotId)
  }

  getCurrentParkingLotTransactions() {
    const parkingLotId = this.getCurrentParkingLotId()
    return this.getTransactionsByParkingLot(parkingLotId)
  }

  saveTransactions(transactions) {
    localStorage.setItem('transactions', JSON.stringify(transactions))
  }

  addTransaction(transaction) {
    const transactions = this.getTransactions()
    transaction.id = Date.now().toString()
    transaction.createdAt = new Date().toISOString()
    transactions.unshift(transaction) // 最新在前
    this.saveTransactions(transactions)
    return transaction
  }

  // 设置管理
  getSettings() {
    return JSON.parse(localStorage.getItem('settings') || '{}')
  }

  updateSettings(updates) {
    const settings = this.getSettings()
    const newSettings = { ...settings, ...updates }
    localStorage.setItem('settings', JSON.stringify(newSettings))
    return newSettings
  }

  // 统计方法
  getTodayStats() {
    const vehicles = this.getVehicles()
    const today = new Date().toDateString()

    const todayVehicles = vehicles.filter(v => {
      const entryDate = new Date(v.entryTime).toDateString()
      return entryDate === today
    })

    const transactions = this.getTransactions()
    const todayTransactions = transactions.filter(t => {
      const transDate = new Date(t.createdAt).toDateString()
      return transDate === today
    })

    const totalRevenue = todayTransactions.reduce((sum, t) => sum + t.amount, 0)

    return {
      vehicleCount: todayVehicles.length,
      revenue: totalRevenue,
      completedTransactions: todayTransactions.length,
    }
  }

  getParkingLotStats(parkingLotId) {
    const vehicles = this.getVehicles()
    const parkingLotVehicles = vehicles.filter(
      v => v.parkingLotId === parkingLotId && v.status === 'parked'
    )

    const parkingLots = this.getParkingLots()
    const parkingLot = parkingLots.find(p => p.id === parkingLotId)

    if (!parkingLot) return null

    const totalSpaces = parkingLot.totalSpaces
    const occupiedSpaces = parkingLotVehicles.length
    const availableSpaces = totalSpaces - occupiedSpaces
    const usageRate = Math.round((occupiedSpaces / totalSpaces) * 100)

    return {
      totalSpaces,
      occupiedSpaces,
      availableSpaces,
      usageRate,
    }
  }

  // 工具方法
  calculateParkingFee(entryTime, exitTime, hourlyRate) {
    const entry = new Date(entryTime)
    const exit = new Date(exitTime)
    const diffMs = exit - entry
    const diffHours = Math.ceil(diffMs / (1000 * 60 * 60))

    const settings = this.getSettings()
    const freeMinutes = settings.freeMinutes || 15

    // 免费时段
    if (diffMs <= freeMinutes * 60 * 1000) {
      return 0
    }

    return Math.max(1, diffHours) * hourlyRate
  }

  // 数据导出导入
  exportData() {
    return {
      parkingLots: this.getParkingLots(),
      vehicles: this.getVehicles(),
      transactions: this.getTransactions(),
      settings: this.getSettings(),
      exportedAt: new Date().toISOString(),
    }
  }

  importData(data) {
    if (data.parkingLots) {
      localStorage.setItem('parkingLots', JSON.stringify(data.parkingLots))
    }
    if (data.vehicles) {
      localStorage.setItem('vehicles', JSON.stringify(data.vehicles))
    }
    if (data.transactions) {
      localStorage.setItem('transactions', JSON.stringify(data.transactions))
    }
    if (data.settings) {
      localStorage.setItem('settings', JSON.stringify(data.settings))
    }
  }
}

// 全局存储实例
const storage = new ParkingStorage()

// 确保初始化完成后重新检查当前停车场设置
setTimeout(() => {
  storage.initializeCurrentParkingLot()
  console.log('Storage 初始化完成，当前停车场ID:', storage.getCurrentParkingLotId())
  console.log('当前停车场数据:', storage.getCurrentParkingLot())
}, 100)
