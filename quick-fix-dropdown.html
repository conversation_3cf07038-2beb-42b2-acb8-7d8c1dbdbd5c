<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车场下拉菜单快速修复</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8">停车场下拉菜单快速修复</h1>
        
        <!-- 测试按钮 -->
        <div class="glass-card p-6 rounded-2xl mb-8">
            <h2 class="text-xl font-semibold mb-4">测试控制</h2>
            <div class="space-x-4">
                <button onclick="runDiagnosis()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg">
                    运行诊断
                </button>
                <button onclick="showDropdown()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg">
                    显示下拉菜单
                </button>
                <button onclick="hideDropdown()" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg">
                    隐藏下拉菜单
                </button>
                <button onclick="fixDropdown()" class="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded-lg">
                    修复下拉菜单
                </button>
            </div>
        </div>
        
        <!-- 复制主页面的停车场切换器结构 -->
        <div class="glass-card p-6 rounded-2xl mb-8">
            <h2 class="text-xl font-semibold mb-4">停车场切换器</h2>
            
            <div class="relative" id="parkingSwitcher">
                <button class="flex items-center space-x-2 px-3 py-2 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <span class="text-sm text-gray-300" id="currentParkingName">选择停车场</span>
                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                
                <div class="absolute right-0 mt-2 w-64 bg-gray-800 rounded-lg shadow-lg opacity-0 invisible transition-all duration-200 transform scale-95" id="parkingDropdown">
                    <div class="py-2 max-h-60 overflow-y-auto" id="parkingListDropdown">
                        <!-- 停车场列表将通过JS动态生成 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 日志输出 -->
        <div class="glass-card p-6 rounded-2xl">
            <h2 class="text-xl font-semibold mb-4">日志输出</h2>
            <pre id="logOutput" class="bg-gray-800 p-4 rounded text-sm overflow-auto max-h-96 whitespace-pre-wrap"></pre>
        </div>
    </div>

    <script src="js/storage.js"></script>
    <script>
        function log(message) {
            const output = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function runDiagnosis() {
            log('=== 开始诊断 ===');
            
            // 检查storage
            if (typeof storage === 'undefined') {
                log('❌ storage 对象未定义');
                return;
            }
            
            const parkingLots = storage.getParkingLots();
            log(`✅ 获取到 ${parkingLots.length} 个停车场`);
            
            // 检查DOM元素
            const switcher = document.getElementById('parkingSwitcher');
            const dropdown = document.getElementById('parkingDropdown');
            const dropdownList = document.getElementById('parkingListDropdown');
            const nameElement = document.getElementById('currentParkingName');
            
            log(`DOM检查: switcher=${!!switcher}, dropdown=${!!dropdown}, list=${!!dropdownList}, name=${!!nameElement}`);
            
            if (dropdown) {
                const style = window.getComputedStyle(dropdown);
                log(`下拉菜单样式: opacity=${style.opacity}, visibility=${style.visibility}, display=${style.display}`);
                log(`下拉菜单类名: ${dropdown.className}`);
            }
            
            // 渲染停车场列表
            renderParkingList();
        }

        function renderParkingList() {
            log('开始渲染停车场列表');
            
            const dropdownList = document.getElementById('parkingListDropdown');
            const nameElement = document.getElementById('currentParkingName');
            
            if (!dropdownList || !nameElement) {
                log('❌ 必要的DOM元素不存在');
                return;
            }
            
            if (typeof storage === 'undefined') {
                log('❌ storage 对象未定义');
                return;
            }
            
            const parkingLots = storage.getParkingLots();
            const currentId = storage.getCurrentParkingLotId();
            
            log(`渲染 ${parkingLots.length} 个停车场，当前ID: ${currentId}`);
            
            // 清空列表
            dropdownList.innerHTML = '';
            
            if (parkingLots.length === 0) {
                dropdownList.innerHTML = '<div class="px-4 py-3 text-sm text-gray-400 text-center">暂无停车场</div>';
                log('显示"暂无停车场"消息');
                return;
            }
            
            // 渲染停车场选项
            parkingLots.forEach((parking, index) => {
                const isCurrent = parking.id === currentId;
                
                const button = document.createElement('button');
                button.className = `w-full text-left px-4 py-3 hover:bg-gray-700 transition-colors ${isCurrent ? 'bg-blue-600/20' : ''}`;
                
                const content = document.createElement('div');
                content.className = 'flex items-center justify-between';
                
                const leftDiv = document.createElement('div');
                leftDiv.className = 'flex items-center space-x-3';
                
                const checkmark = document.createElement('span');
                if (isCurrent) {
                    checkmark.className = 'text-blue-400';
                    checkmark.textContent = '✓';
                } else {
                    checkmark.className = 'w-4';
                }
                
                const name = document.createElement('span');
                name.className = `text-sm ${isCurrent ? 'text-white font-medium' : 'text-gray-300'}`;
                name.textContent = parking.name;
                
                const location = document.createElement('div');
                location.className = 'text-xs text-gray-400 mt-1 ml-7';
                location.textContent = parking.location || '未设置位置';
                
                leftDiv.appendChild(checkmark);
                leftDiv.appendChild(name);
                content.appendChild(leftDiv);
                button.appendChild(content);
                button.appendChild(location);
                
                // 添加点击事件
                button.addEventListener('click', () => {
                    log(`点击了停车场: ${parking.name}`);
                    storage.setCurrentParkingLotId(parking.id);
                    nameElement.textContent = parking.name;
                    renderParkingList(); // 重新渲染
                    hideDropdown(); // 隐藏下拉菜单
                });
                
                dropdownList.appendChild(button);
                log(`添加停车场选项: ${parking.name} ${isCurrent ? '(当前)' : ''}`);
            });
            
            // 更新当前停车场名称
            const currentParking = storage.getCurrentParkingLot();
            if (currentParking && nameElement) {
                nameElement.textContent = currentParking.name;
                nameElement.classList.remove('text-gray-300');
                nameElement.classList.add('text-white', 'font-medium');
                log(`更新当前停车场名称: ${currentParking.name}`);
            }
            
            log('✅ 停车场列表渲染完成');
        }

        function showDropdown() {
            const dropdown = document.getElementById('parkingDropdown');
            if (dropdown) {
                log('手动显示下拉菜单');
                dropdown.classList.remove('opacity-0', 'invisible', 'scale-95');
                dropdown.classList.add('opacity-100', 'visible', 'scale-100');
                log(`下拉菜单类名: ${dropdown.className}`);
            } else {
                log('❌ 下拉菜单元素不存在');
            }
        }

        function hideDropdown() {
            const dropdown = document.getElementById('parkingDropdown');
            if (dropdown) {
                log('手动隐藏下拉菜单');
                dropdown.classList.add('opacity-0', 'invisible', 'scale-95');
                dropdown.classList.remove('opacity-100', 'visible', 'scale-100');
                log(`下拉菜单类名: ${dropdown.className}`);
            } else {
                log('❌ 下拉菜单元素不存在');
            }
        }

        function fixDropdown() {
            log('=== 开始修复下拉菜单 ===');
            
            // 1. 重新渲染数据
            renderParkingList();
            
            // 2. 重新绑定事件
            const switcher = document.getElementById('parkingSwitcher');
            const dropdown = document.getElementById('parkingDropdown');
            
            if (switcher && dropdown) {
                log('重新绑定悬停事件');
                
                // 移除旧的事件监听器（如果有的话）
                const newSwitcher = switcher.cloneNode(true);
                switcher.parentNode.replaceChild(newSwitcher, switcher);
                
                // 重新获取元素引用
                const freshSwitcher = document.getElementById('parkingSwitcher');
                const freshDropdown = document.getElementById('parkingDropdown');
                
                if (freshSwitcher && freshDropdown) {
                    freshSwitcher.addEventListener('mouseenter', () => {
                        log('鼠标进入 - 显示下拉菜单');
                        freshDropdown.classList.remove('opacity-0', 'invisible', 'scale-95');
                        freshDropdown.classList.add('opacity-100', 'visible', 'scale-100');
                    });
                    
                    freshSwitcher.addEventListener('mouseleave', () => {
                        log('鼠标离开 - 隐藏下拉菜单');
                        freshDropdown.classList.add('opacity-0', 'invisible', 'scale-95');
                        freshDropdown.classList.remove('opacity-100', 'visible', 'scale-100');
                    });
                    
                    log('✅ 事件重新绑定完成');
                } else {
                    log('❌ 重新获取元素失败');
                }
            } else {
                log('❌ 无法找到必要的DOM元素');
            }
            
            log('=== 修复完成 ===');
        }

        // 页面加载完成后自动运行诊断
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('页面加载完成，开始自动诊断');
                runDiagnosis();
            }, 500);
        });
    </script>
</body>
</html>
