<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帮助中心 - 智慧停车场系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div id="header"></div>
    <div id="sidebar"></div>
    
    <main class="ml-0 lg:ml-64 pt-16 p-6">
        <div class="max-w-4xl mx-auto">
            <div class="mb-8">
                <h1 class="text-2xl font-bold">帮助中心</h1>
                <p class="text-gray-400">系统使用指南和常见问题解答</p>
            </div>

            <!-- 快速指南 -->
            <div class="glass-card p-6 rounded-2xl mb-8">
                <h2 class="text-xl font-semibold mb-6">快速使用指南</h2>
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-semibold mb-3 flex items-center">
                            <span class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-sm mr-2">1</span>
                            添加停车场
                        </h3>
                        <p class="text-gray-300">在"停车场管理"页面中添加您的停车场信息，包括名称、位置、总车位数和小时费率。</p>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold mb-3 flex items-center">
                            <span class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-sm mr-2">2</span>
                            车辆入场登记
                        </h3>
                        <p class="text-gray-300">在"车辆进出"页面中输入车牌号码并选择停车场，系统会自动记录入场时间。</p>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold mb-3 flex items-center">
                            <span class="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-sm mr-2">3</span>
                            车辆出场结算
                        </h3>
                        <p class="text-gray-300">车辆离开时，在"车辆进出"页面切换到出场模式，输入车牌号，系统会自动计算停车费用。</p>
                    </div>

                    <div>
                        <h3 class="text-lg font-semibold mb-3 flex items-center">
                            <span class="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-sm mr-2">4</span>
                            查看统计数据
                        </h3>
                        <p class="text-gray-300">在首页Dashboard和"收费记录"页面中可以查看收入统计、车流量分析等运营数据。</p>
                    </div>
                </div>
            </div>

            <!-- 常见问题 -->
            <div class="glass-card p-6 rounded-2xl mb-8">
                <h2 class="text-xl font-semibold mb-6">常见问题解答</h2>
                <div class="space-y-4">
                    <div class="bg-gray-800/50 rounded-lg p-4">
                        <h3 class="font-semibold mb-2">Q: 如何修改停车场信息？</h3>
                        <p class="text-gray-300">A: 在"停车场管理"页面中点击相应停车场的"编辑"按钮即可修改信息。</p>
                    </div>

                    <div class="bg-gray-800/50 rounded-lg p-4">
                        <h3 class="font-semibold mb-2">Q: 数据会丢失吗？</h3>
                        <p class="text-gray-300">A: 数据存储在浏览器本地，清除浏览器缓存会导致数据丢失。建议定期在"系统设置"中导出备份。</p>
                    </div>

                    <div class="bg-gray-800/50 rounded-lg p-4">
                        <h3 class="font-semibold mb-2">Q: 支持多个停车场同时管理吗？</h3>
                        <p class="text-gray-300">A: 支持！您可以添加多个停车场，并在各页面中切换查看不同停车场的数据。</p>
                    </div>

                    <div class="bg-gray-800/50 rounded-lg p-4">
                        <h3 class="font-semibold mb-2">Q: 如何计算停车费用？</h3>
                        <p class="text-gray-300">A: 系统根据设置的小时费率和实际停车时间自动计算，支持设置免费时长。</p>
                    </div>
                </div>
            </div>

            <!-- 联系支持 -->
            <div class="glass-card p-6 rounded-2xl">
                <h2 class="text-xl font-semibold mb-4">需要更多帮助？</h2>
                <p class="text-gray-300 mb-4">如果您遇到其他问题或需要技术支持，请联系我们：</p>
                <div class="space-y-2">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        <span>邮箱：<EMAIL></span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        <span>电话：400-123-4567</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="js/components.js"></script>
</body>
</html>