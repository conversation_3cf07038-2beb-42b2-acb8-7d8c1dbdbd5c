// 停车场下拉菜单诊断脚本

window.diagnoseParkingDropdown = function() {
    console.log('=== 停车场下拉菜单诊断开始 ===');
    
    // 1. 检查数据加载
    console.log('\n1. 📊 数据加载检查:');
    if (typeof storage === 'undefined') {
        console.error('❌ storage 对象未定义');
        return;
    }
    
    const parkingLots = storage.getParkingLots();
    const currentId = storage.getCurrentParkingLotId();
    const currentParking = storage.getCurrentParkingLot();
    
    console.log(`✅ 停车场数量: ${parkingLots.length}`);
    console.log(`✅ 当前停车场ID: ${currentId}`);
    console.log(`✅ 当前停车场: ${currentParking ? currentParking.name : '无'}`);
    
    parkingLots.forEach((parking, index) => {
        console.log(`   ${index + 1}. ${parking.name} (ID: ${parking.id})`);
    });
    
    // 2. 检查DOM结构
    console.log('\n2. 🏗️ DOM结构检查:');
    const switcher = document.getElementById('parkingSwitcher');
    const dropdown = document.getElementById('parkingDropdown');
    const dropdownList = document.getElementById('parkingListDropdown');
    const currentNameElement = document.getElementById('currentParkingName');
    
    console.log(`parkingSwitcher 存在: ${!!switcher}`);
    console.log(`parkingDropdown 存在: ${!!dropdown}`);
    console.log(`parkingListDropdown 存在: ${!!dropdownList}`);
    console.log(`currentParkingName 存在: ${!!currentNameElement}`);
    
    if (switcher) {
        console.log(`switcher 类名: ${switcher.className}`);
        console.log(`switcher 子元素数量: ${switcher.children.length}`);
    }
    
    if (dropdown) {
        console.log(`dropdown 类名: ${dropdown.className}`);
        console.log(`dropdown 子元素数量: ${dropdown.children.length}`);
        
        // 检查样式
        const computedStyle = window.getComputedStyle(dropdown);
        console.log(`dropdown 计算样式:`);
        console.log(`  - display: ${computedStyle.display}`);
        console.log(`  - visibility: ${computedStyle.visibility}`);
        console.log(`  - opacity: ${computedStyle.opacity}`);
        console.log(`  - z-index: ${computedStyle.zIndex}`);
        console.log(`  - position: ${computedStyle.position}`);
        console.log(`  - transform: ${computedStyle.transform}`);
    }
    
    if (dropdownList) {
        console.log(`dropdownList 子元素数量: ${dropdownList.children.length}`);
        console.log(`dropdownList innerHTML 长度: ${dropdownList.innerHTML.length}`);
        
        // 显示前几个子元素的信息
        Array.from(dropdownList.children).slice(0, 3).forEach((child, index) => {
            console.log(`  子元素 ${index + 1}: ${child.tagName} - ${child.textContent.substring(0, 50)}...`);
        });
    }
    
    // 3. 检查事件绑定
    console.log('\n3. 🎯 事件绑定检查:');
    
    // 检查ParkingSwitcher实例
    if (window.parkingSwitcher) {
        console.log('✅ ParkingSwitcher 实例存在');
        
        // 检查事件监听器（这个比较难直接检查，我们通过模拟事件来测试）
        if (switcher) {
            console.log('🧪 测试鼠标悬停事件...');
            
            // 模拟mouseenter事件
            const mouseEnterEvent = new MouseEvent('mouseenter', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            
            switcher.dispatchEvent(mouseEnterEvent);
            
            // 检查下拉菜单是否显示
            setTimeout(() => {
                if (dropdown) {
                    const isVisible = dropdown.classList.contains('opacity-100') && 
                                    dropdown.classList.contains('visible') && 
                                    dropdown.classList.contains('scale-100');
                    console.log(`鼠标悬停后下拉菜单可见: ${isVisible}`);
                    
                    if (isVisible) {
                        console.log('✅ 悬停事件正常工作');
                    } else {
                        console.log('❌ 悬停事件可能有问题');
                        console.log(`当前类名: ${dropdown.className}`);
                    }
                    
                    // 模拟mouseleave事件
                    const mouseLeaveEvent = new MouseEvent('mouseleave', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    
                    switcher.dispatchEvent(mouseLeaveEvent);
                    
                    setTimeout(() => {
                        const isHidden = dropdown.classList.contains('opacity-0') && 
                                       dropdown.classList.contains('invisible') && 
                                       dropdown.classList.contains('scale-95');
                        console.log(`鼠标离开后下拉菜单隐藏: ${isHidden}`);
                    }, 100);
                }
            }, 100);
        }
    } else {
        console.error('❌ ParkingSwitcher 实例不存在');
    }
    
    // 4. 手动显示下拉菜单测试
    console.log('\n4. 🔧 手动显示测试:');
    
    window.showDropdownManually = function() {
        if (dropdown) {
            console.log('手动显示下拉菜单...');
            dropdown.classList.remove('opacity-0', 'invisible', 'scale-95');
            dropdown.classList.add('opacity-100', 'visible', 'scale-100');
            console.log('✅ 手动显示完成');
            
            setTimeout(() => {
                console.log('3秒后自动隐藏...');
                dropdown.classList.add('opacity-0', 'invisible', 'scale-95');
                dropdown.classList.remove('opacity-100', 'visible', 'scale-100');
            }, 3000);
        } else {
            console.error('❌ dropdown 元素不存在，无法手动显示');
        }
    };
    
    window.hideDropdownManually = function() {
        if (dropdown) {
            console.log('手动隐藏下拉菜单...');
            dropdown.classList.add('opacity-0', 'invisible', 'scale-95');
            dropdown.classList.remove('opacity-100', 'visible', 'scale-100');
            console.log('✅ 手动隐藏完成');
        }
    };
    
    // 5. 提供修复建议
    console.log('\n5. 💡 修复建议:');
    
    if (!switcher || !dropdown || !dropdownList) {
        console.log('❌ 关键DOM元素缺失，需要检查组件加载');
    } else if (dropdownList.children.length === 0) {
        console.log('❌ 下拉列表为空，需要检查数据渲染');
    } else {
        console.log('✅ 基本结构正常');
        console.log('💡 可以尝试运行以下命令进行测试:');
        console.log('   - showDropdownManually() // 手动显示下拉菜单');
        console.log('   - hideDropdownManually() // 手动隐藏下拉菜单');
        console.log('   - refreshParkingSwitcher() // 刷新停车场切换器');
    }
    
    console.log('\n=== 停车场下拉菜单诊断结束 ===');
};

// 页面加载完成后自动运行诊断
window.addEventListener('load', function() {
    setTimeout(() => {
        console.log('自动运行停车场下拉菜单诊断...');
        window.diagnoseParkingDropdown();
    }, 2000);
});

console.log('停车场下拉菜单诊断脚本已加载');
console.log('可以在控制台运行 diagnoseParkingDropdown() 进行诊断');
