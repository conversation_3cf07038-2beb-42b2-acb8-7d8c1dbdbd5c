<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-6">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">停车场切换器调试</h1>
        
        <div class="space-y-4">
            <div class="bg-gray-800 p-4 rounded">
                <h2 class="text-lg font-semibold mb-2">依赖检查</h2>
                <p>storage: <span id="storageStatus" class="font-mono"></span></p>
                <p>componentLoader: <span id="componentStatus" class="font-mono"></span></p>
                <p>ParkingSwitcher: <span id="switcherStatus" class="font-mono"></span></p>
                <p>parkingSwitcher实例: <span id="instanceStatus" class="font-mono"></span></p>
            </div>
            
            <div class="bg-gray-800 p-4 rounded">
                <h2 class="text-lg font-semibold mb-2">数据检查</h2>
                <p>停车场数量: <span id="parkingCount" class="font-mono"></span></p>
                <p>当前停车场ID: <span id="currentId" class="font-mono"></span></p>
                <p>当前停车场名称: <span id="currentName" class="font-mono"></span></p>
            </div>
            
            <div class="bg-gray-800 p-4 rounded">
                <h2 class="text-lg font-semibold mb-2">操作</h2>
                <button onclick="loadDependencies()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded mr-2">加载依赖</button>
                <button onclick="initSwitcher()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded mr-2">初始化切换器</button>
                <button onclick="checkStatus()" class="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded">检查状态</button>
            </div>
        </div>
    </div>

    <script>
        function loadDependencies() {
            const scripts = [
                'js/storage.js',
                'js/components.js'
            ];
            
            scripts.forEach(src => {
                const script = document.createElement('script');
                script.src = src;
                document.head.appendChild(script);
            });
            
            setTimeout(checkStatus, 500);
        }
        
        function initSwitcher() {
            if (typeof ParkingSwitcher !== 'undefined' && typeof storage !== 'undefined' && typeof componentLoader !== 'undefined') {
                window.parkingSwitcher = new ParkingSwitcher();
                checkStatus();
            } else {
                alert('依赖未加载完成');
            }
        }
        
        function checkStatus() {
            document.getElementById('storageStatus').textContent = typeof storage;
            document.getElementById('componentStatus').textContent = typeof componentLoader;
            document.getElementById('switcherStatus').textContent = typeof ParkingSwitcher;
            document.getElementById('instanceStatus').textContent = typeof window.parkingSwitcher;
            
            if (typeof storage !== 'undefined') {
                const parkingLots = storage.getParkingLots();
                const currentId = storage.getCurrentParkingLotId();
                const currentParking = storage.getCurrentParkingLot();
                
                document.getElementById('parkingCount').textContent = parkingLots.length;
                document.getElementById('currentId').textContent = currentId || '无';
                document.getElementById('currentName').textContent = currentParking ? currentParking.name : '无';
            }
        }
        
        // 页面加载时检查
        document.addEventListener('DOMContentLoaded', checkStatus);
    </script>
</body>
</html>