// 调试停车场数据加载问题的脚本

// 等待页面完全加载后再执行调试
window.addEventListener('load', function () {
  setTimeout(function () {
    console.log('=== 停车场数据调试开始 ===')

    // 1. 检查localStorage中的原始数据
    console.log('1. LocalStorage 原始数据:')
    console.log('parkingLots:', localStorage.getItem('parkingLots'))
    console.log('currentParkingLotId:', localStorage.getItem('currentParkingLotId'))

    // 2. 检查storage对象是否存在
    console.log('2. Storage 对象检查:')
    console.log('typeof storage:', typeof storage)
    if (typeof storage !== 'undefined') {
      console.log('storage.getParkingLots:', storage.getParkingLots())
      console.log('storage.getCurrentParkingLotId():', storage.getCurrentParkingLotId())
      console.log('storage.getCurrentParkingLot():', storage.getCurrentParkingLot())
    } else {
      console.log('storage 对象未定义')
    }

    // 3. 检查DOM元素是否存在
    console.log('3. DOM 元素检查:')
    const parkingDropdown = document.getElementById('parkingListDropdown')
    const currentParkingName = document.getElementById('currentParkingName')
    console.log('parkingListDropdown 元素:', parkingDropdown)
    console.log('currentParkingName 元素:', currentParkingName)

    // 4. 检查ParkingSwitcher是否初始化
    console.log('4. ParkingSwitcher 检查:')
    console.log('window.parkingSwitcher:', window.parkingSwitcher)

    // 5. 手动尝试渲染停车场列表
    console.log('5. 手动渲染测试:')
    if (typeof storage !== 'undefined' && parkingDropdown) {
      const parkingLots = storage.getParkingLots()
      console.log('获取到的停车场数据:', parkingLots)

      if (parkingLots.length > 0) {
        console.log('尝试手动渲染停车场列表...')
        parkingDropdown.innerHTML = ''

        parkingLots.forEach((parking, index) => {
          const button = document.createElement('button')
          button.className = 'w-full text-left px-4 py-3 hover:bg-gray-700 transition-colors'
          button.textContent = parking.name + ' - ' + (parking.location || '未设置位置')
          button.onclick = () => {
            console.log('点击了停车场:', parking.name)
            if (typeof storage !== 'undefined') {
              storage.setCurrentParkingLotId(parking.id)
              if (currentParkingName) {
                currentParkingName.textContent = parking.name
              }
            }
          }
          parkingDropdown.appendChild(button)
          console.log('添加了停车场按钮:', parking.name)
        })

        // 更新当前停车场名称
        if (currentParkingName) {
          const currentParking = storage.getCurrentParkingLot()
          if (currentParking) {
            currentParkingName.textContent = currentParking.name
            console.log('更新了当前停车场名称:', currentParking.name)
          }
        }
      } else {
        console.log('没有停车场数据')
        parkingDropdown.innerHTML =
          '<div class="px-4 py-3 text-sm text-gray-400 text-center">暂无停车场</div>'
      }
    }

    // 6. 添加一个全局调试函数
    window.debugParking = function () {
      console.log('=== 手动调试停车场 ===')
      console.log('LocalStorage parkingLots:', localStorage.getItem('parkingLots'))
      console.log(
        'Storage getParkingLots:',
        typeof storage !== 'undefined' ? storage.getParkingLots() : 'storage未定义'
      )
      console.log('ParkingSwitcher:', window.parkingSwitcher)

      // 强制刷新
      if (window.refreshParkingSwitcher) {
        console.log('调用 refreshParkingSwitcher')
        window.refreshParkingSwitcher()
      }
    }

    console.log('=== 停车场数据调试结束 ===')
    console.log('可以在控制台调用 debugParking() 进行手动调试')
  }, 1000) // 延迟1秒执行，确保所有组件都已加载
})
