<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车场切换器测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8">停车场切换器测试</h1>
        
        <!-- 手动创建停车场切换器 -->
        <div class="glass-card p-6 rounded-2xl mb-8">
            <h2 class="text-xl font-semibold mb-4">停车场切换器</h2>
            
            <div class="relative" id="parkingSwitcher">
                <button class="flex items-center space-x-2 px-3 py-2 bg-gray-700 rounded-lg hover:bg-gray-600 transition-colors">
                    <span class="text-sm text-gray-300" id="currentParkingName">选择停车场</span>
                    <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                
                <div class="absolute right-0 mt-2 w-64 bg-gray-800 rounded-lg shadow-lg opacity-0 invisible transition-all duration-200 transform scale-95" id="parkingDropdown">
                    <div class="py-2 max-h-60 overflow-y-auto" id="parkingListDropdown">
                        <!-- 停车场列表将通过JS动态生成 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 调试信息 -->
        <div class="glass-card p-6 rounded-2xl">
            <h2 class="text-xl font-semibold mb-4">调试信息</h2>
            <div class="space-y-4">
                <button onclick="testParkingSwitcher()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg">
                    测试停车场切换器
                </button>
                <button onclick="showDebugInfo()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg">
                    显示调试信息
                </button>
                <button onclick="manualRender()" class="bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded-lg">
                    手动渲染
                </button>
            </div>
            <pre id="debugOutput" class="mt-4 bg-gray-800 p-4 rounded text-sm overflow-auto max-h-64"></pre>
        </div>
    </div>

    <script src="js/storage.js"></script>
    <script>
        function log(message) {
            const output = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function testParkingSwitcher() {
            log('=== 测试停车场切换器 ===');
            
            // 检查storage
            if (typeof storage === 'undefined') {
                log('❌ storage 对象未定义');
                return;
            }
            
            const parkingLots = storage.getParkingLots();
            log(`✅ 获取到 ${parkingLots.length} 个停车场`);
            
            // 检查DOM元素
            const dropdown = document.getElementById('parkingListDropdown');
            const nameElement = document.getElementById('currentParkingName');
            
            if (!dropdown) {
                log('❌ parkingListDropdown 元素未找到');
                return;
            }
            
            if (!nameElement) {
                log('❌ currentParkingName 元素未找到');
                return;
            }
            
            log('✅ DOM 元素检查通过');
            
            // 手动渲染停车场列表
            manualRender();
        }

        function showDebugInfo() {
            log('=== 调试信息 ===');
            log(`Storage 类型: ${typeof storage}`);
            
            if (typeof storage !== 'undefined') {
                const parkingLots = storage.getParkingLots();
                const currentId = storage.getCurrentParkingLotId();
                const currentParking = storage.getCurrentParkingLot();
                
                log(`停车场数量: ${parkingLots.length}`);
                log(`当前停车场ID: ${currentId}`);
                log(`当前停车场: ${currentParking ? currentParking.name : '无'}`);
                
                parkingLots.forEach((parking, index) => {
                    log(`  ${index + 1}. ${parking.name} (${parking.location}) - ID: ${parking.id}`);
                });
            }
            
            const dropdown = document.getElementById('parkingListDropdown');
            const nameElement = document.getElementById('currentParkingName');
            
            log(`parkingListDropdown 存在: ${!!dropdown}`);
            log(`currentParkingName 存在: ${!!nameElement}`);
            log(`currentParkingName 内容: ${nameElement ? nameElement.textContent : 'N/A'}`);
        }

        function manualRender() {
            log('=== 手动渲染停车场列表 ===');
            
            const dropdown = document.getElementById('parkingListDropdown');
            const nameElement = document.getElementById('currentParkingName');
            
            if (!dropdown || !nameElement) {
                log('❌ 必要的DOM元素不存在');
                return;
            }
            
            if (typeof storage === 'undefined') {
                log('❌ storage 对象未定义');
                return;
            }
            
            const parkingLots = storage.getParkingLots();
            const currentId = storage.getCurrentParkingLotId();
            
            log(`开始渲染 ${parkingLots.length} 个停车场`);
            
            // 清空下拉列表
            dropdown.innerHTML = '';
            
            if (parkingLots.length === 0) {
                dropdown.innerHTML = '<div class="px-4 py-3 text-sm text-gray-400 text-center">暂无停车场</div>';
                log('显示"暂无停车场"消息');
                return;
            }
            
            // 渲染停车场列表
            parkingLots.forEach((parking, index) => {
                const isCurrent = parking.id === currentId;
                
                const button = document.createElement('button');
                button.className = `w-full text-left px-4 py-3 hover:bg-gray-700 transition-colors ${isCurrent ? 'bg-blue-600/20' : ''}`;
                
                const content = document.createElement('div');
                content.className = 'flex items-center justify-between';
                
                const leftDiv = document.createElement('div');
                leftDiv.className = 'flex items-center space-x-3';
                
                const checkmark = document.createElement('span');
                if (isCurrent) {
                    checkmark.className = 'text-blue-400';
                    checkmark.textContent = '✓';
                } else {
                    checkmark.className = 'w-4';
                }
                
                const name = document.createElement('span');
                name.className = `text-sm ${isCurrent ? 'text-white font-medium' : 'text-gray-300'}`;
                name.textContent = parking.name;
                
                const location = document.createElement('div');
                location.className = 'text-xs text-gray-400 mt-1 ml-7';
                location.textContent = parking.location || '未设置位置';
                
                leftDiv.appendChild(checkmark);
                leftDiv.appendChild(name);
                content.appendChild(leftDiv);
                button.appendChild(content);
                button.appendChild(location);
                
                // 添加点击事件
                button.addEventListener('click', () => {
                    log(`点击了停车场: ${parking.name}`);
                    storage.setCurrentParkingLotId(parking.id);
                    nameElement.textContent = parking.name;
                    manualRender(); // 重新渲染以更新选中状态
                });
                
                dropdown.appendChild(button);
                log(`添加停车场: ${parking.name} ${isCurrent ? '(当前)' : ''}`);
            });
            
            // 更新当前停车场名称
            const currentParking = storage.getCurrentParkingLot();
            if (currentParking) {
                nameElement.textContent = currentParking.name;
                nameElement.classList.remove('text-gray-300');
                nameElement.classList.add('text-white', 'font-medium');
                log(`更新当前停车场名称: ${currentParking.name}`);
            }
            
            log('✅ 手动渲染完成');
        }

        // 设置悬停效果
        function setupHoverEffect() {
            const switcher = document.getElementById('parkingSwitcher');
            const dropdown = document.getElementById('parkingDropdown');
            
            if (switcher && dropdown) {
                switcher.addEventListener('mouseenter', () => {
                    dropdown.classList.remove('opacity-0', 'invisible', 'scale-95');
                    dropdown.classList.add('opacity-100', 'visible', 'scale-100');
                });
                
                switcher.addEventListener('mouseleave', () => {
                    dropdown.classList.add('opacity-0', 'invisible', 'scale-95');
                    dropdown.classList.remove('opacity-100', 'visible', 'scale-100');
                });
                
                log('✅ 悬停效果设置完成');
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('页面加载完成，开始初始化');
                setupHoverEffect();
                showDebugInfo();
                manualRender();
            }, 500);
        });
    </script>
</body>
</html>
