# 智慧停车场管理系统

一个基于纯前端技术开发的停车场管理系统，使用 HTML、CSS、JavaScript 和 TailwindCSS 构建，数据存储在浏览器本地。

## 功能特性

### 🅿️ 核心功能
- **多停车场管理** - 支持添加和管理多个停车场
- **车辆进出管理** - 完整的入场登记和出场结算流程
- **实时车位监控** - 图形化显示车位使用状态
- **自动计费系统** - 根据时长自动计算停车费用
- **数据统计报表** - 收入统计、车流量分析等功能

### 🎨 界面设计
- **现代玻璃态设计** - 毛玻璃效果和渐变背景
- **深色主题** - 专业的深色界面，减少视觉疲劳
- **响应式布局** - 完美适配桌面、平板和移动设备
- **实时动画** - 数字滚动动画和状态指示器

### 💾 数据管理
- **LocalStorage存储** - 所有数据本地存储，无需后端
- **数据导入导出** - 支持JSON格式数据备份和恢复
- **实时数据同步** - 多页面间数据实时更新

## 项目结构

```
parking/
├── index.html              # 首页Dashboard
├── parking-management.html # 停车场管理
├── vehicle-entry.html      # 车辆进出管理
├── parking-status.html     # 车位状态监控
├── billing-records.html    # 收费记录查询
├── reports.html            # 统计报表
├── settings.html           # 系统设置
├── help.html               # 帮助中心
├── test.html               # 功能测试页面
├── css/
│   └── style.css           # 自定义样式
├── js/
│   ├── storage.js          # 数据存储管理
│   ├── components.js       # 组件加载器
│   ├── dashboard.js        # 首页功能
│   ├── parking-management.js
│   ├── vehicle-entry.js
│   ├── parking-status.js
│   └── billing-records.js
├── components/
│   ├── header.html         # 头部导航组件
│   └── sidebar.html        # 侧边栏菜单组件
└── data/                   # 数据目录（预留）
```

## 快速开始

1. **直接打开**：在浏览器中打开 `index.html` 即可使用
2. **本地服务器**（推荐）：使用Python启动本地服务器
   ```bash
   python -m http.server 8000
   ```
   然后在浏览器访问 `http://localhost:8000`

3. **功能测试**：打开 `test.html` 进行功能验证

## 使用说明

### 1. 初始化设置
1. 首先在"停车场管理"页面添加停车场信息
2. 在"系统设置"中配置计费规则
3. 开始记录车辆进出

### 2. 日常操作
- **车辆入场**：在"车辆进出"页面输入车牌号，选择停车场
- **车辆出场**：切换到出场模式，输入车牌号自动计费
- **状态监控**：在"车位状态"页面查看实时车位使用情况
- **数据查询**：在"收费记录"页面查看历史交易

### 3. 数据维护
- **定期备份**：在"系统设置"中导出数据备份
- **数据恢复**：通过导入功能恢复数据
- **清空数据**：可清空所有数据重新开始

## 技术栈

- **前端框架**：纯原生 JavaScript
- **UI框架**：TailwindCSS 3.0
- **图标**：Heroicons + Emoji
- **数据存储**：浏览器 LocalStorage
- **响应式设计**：CSS Grid + Flexbox

## 浏览器支持

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 注意事项

1. **数据持久性**：数据存储在浏览器本地，清除缓存会导致数据丢失
2. **浏览器兼容性**：建议使用现代浏览器以获得最佳体验
3. **性能考虑**：大量数据时可能影响性能，建议定期清理旧数据
4. **安全提示**：此为前端演示系统，不适合生产环境使用

## 开发计划

- [ ] 添加图表可视化（Chart.js集成）
- [ ] 支持车牌识别摄像头接口
- [ ] 添加打印小票功能
- [ ] 实现数据云同步
- [ ] 添加多语言支持

## 许可证

MIT License - 可自由使用和修改