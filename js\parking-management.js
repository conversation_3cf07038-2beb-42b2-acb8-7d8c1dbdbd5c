class ParkingManagement {
    constructor() {
        this.currentEditingId = null;
        this.init();
    }

    init() {
        this.loadParkingList();
        this.setupEventListeners();
        this.checkUrlParams();
    }

    loadParkingList() {
        const parkingLots = storage.getParkingLots();
        const container = document.getElementById('parkingList');
        
        if (parkingLots.length === 0) {
            container.innerHTML = `
                <div class="text-center py-12">
                    <div class="text-gray-400 text-6xl mb-4">🅿️</div>
                    <h3 class="text-gray-300 text-lg mb-2">暂无停车场</h3>
                    <p class="text-gray-500 text-sm mb-4">点击"添加停车场"按钮开始创建</p>
                </div>
            `;
            return;
        }

        let html = '';
        parkingLots.forEach(parking => {
            const stats = storage.getParkingLotStats(parking.id);
            const usageRate = stats ? stats.usageRate : 0;
            const statusColor = this.getStatusColor(usageRate);
            
            html += `
                <div class="glass-card p-4 rounded-xl">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold">🅿️</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-white">${parking.name}</h3>
                                <p class="text-sm text-gray-400">${parking.location || '未设置位置'}</p>
                            </div>
                        </div>
                        <span class="text-xs px-2 py-1 rounded-full ${statusColor}">
                            ${usageRate}% 使用率
                        </span>
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-3">
                        <div>
                            <p class="text-sm text-gray-300">总车位</p>
                            <p class="text-lg font-semibold">${parking.totalSpaces}</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-300">小时费率</p>
                            <p class="text-lg font-semibold text-yellow-400">¥${parking.hourlyRate}</p>
                        </div>
                    </div>

                    <div class="w-full bg-gray-700 rounded-full h-2 mb-3">
                        <div class="bg-gradient-to-r from-green-400 via-yellow-400 to-red-400 h-2 rounded-full" 
                             style="width: ${usageRate}%"></div>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="text-xs text-gray-400">
                            创建于: ${formatDateTime(parking.createdAt)}
                        </span>
                        <div class="flex space-x-2">
                            <button onclick="parkingManagement.editParking('${parking.id}')" 
                                    class="text-blue-400 hover:text-blue-300 px-3 py-1 rounded text-sm">
                                编辑
                            </button>
                            <button onclick="parkingManagement.deleteParking('${parking.id}')" 
                                    class="text-red-400 hover:text-red-300 px-3 py-1 rounded text-sm">
                                删除
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    getStatusColor(usageRate) {
        if (usageRate >= 80) return 'bg-red-500/20 text-red-400';
        if (usageRate >= 50) return 'bg-yellow-500/20 text-yellow-400';
        return 'bg-green-500/20 text-green-400';
    }

    setupEventListeners() {
        // 添加停车场按钮
        document.getElementById('addParkingBtn').addEventListener('click', () => {
            this.openModal();
        });

        // 表单提交
        document.getElementById('parkingForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveParking();
        });

        // 点击模态框背景关闭
        document.getElementById('parkingModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.closeModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });

        // 监听停车场切换事件
        window.addEventListener('parkingLotChanged', () => {
            this.loadParkingList();
        });
    }

    openModal(parkingId = null) {
        this.currentEditingId = parkingId;
        const modal = document.getElementById('parkingModal');
        const title = document.getElementById('modalTitle');
        const form = document.getElementById('parkingForm');

        if (parkingId) {
            // 编辑模式
            title.textContent = '编辑停车场';
            const parking = storage.getParkingLots().find(p => p.id === parkingId);
            if (parking) {
                form.name.value = parking.name;
                form.location.value = parking.location || '';
                form.totalSpaces.value = parking.totalSpaces;
                form.hourlyRate.value = parking.hourlyRate;
            }
        } else {
            // 添加模式
            title.textContent = '添加停车场';
            form.reset();
        }

        modal.classList.remove('hidden');
        modal.classList.add('flex');
    }

    closeModal() {
        const modal = document.getElementById('parkingModal');
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        this.currentEditingId = null;
    }

    saveParking() {
        const form = document.getElementById('parkingForm');
        const formData = {
            name: form.name.value.trim(),
            location: form.location.value.trim(),
            totalSpaces: parseInt(form.totalSpaces.value),
            hourlyRate: parseFloat(form.hourlyRate.value)
        };

        // 验证数据
        if (!formData.name) {
            componentLoader.showToast('请输入停车场名称', 'error');
            return;
        }

        if (formData.totalSpaces <= 0) {
            componentLoader.showToast('车位数必须大于0', 'error');
            return;
        }

        if (formData.hourlyRate < 0) {
            componentLoader.showToast('费率不能为负数', 'error');
            return;
        }

        try {
            if (this.currentEditingId) {
                // 更新现有停车场
                storage.updateParkingLot(this.currentEditingId, formData);
                componentLoader.showToast('停车场更新成功', 'success');
            } else {
                // 添加新停车场
                storage.addParkingLot(formData);
                componentLoader.showToast('停车场添加成功', 'success');
            }

            this.closeModal();
            this.loadParkingList();
            
        } catch (error) {
            console.error('保存停车场失败:', error);
            componentLoader.showToast('保存失败，请重试', 'error');
        }
    }

    editParking(parkingId) {
        this.openModal(parkingId);
    }

    deleteParking(parkingId) {
        if (confirm('确定要删除这个停车场吗？此操作不可恢复。')) {
            try {
                // 检查是否有车辆停放在此停车场
                const vehicles = storage.getVehicles();
                const parkedVehicles = vehicles.filter(v => 
                    v.parkingLotId === parkingId && v.status === 'parked'
                );

                if (parkedVehicles.length > 0) {
                    componentLoader.showToast(`无法删除：仍有 ${parkedVehicles.length} 辆车停放在此`, 'error');
                    return;
                }

                storage.deleteParkingLot(parkingId);
                componentLoader.showToast('停车场删除成功', 'success');
                this.loadParkingList();
                
            } catch (error) {
                console.error('删除停车场失败:', error);
                componentLoader.showToast('删除失败，请重试', 'error');
            }
        }
    }

    checkUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        const editId = urlParams.get('edit');
        
        if (editId) {
            // 检查停车场是否存在
            const parking = storage.getParkingLots().find(p => p.id === editId);
            if (parking) {
                this.openModal(editId);
            } else {
                componentLoader.showToast('找不到指定的停车场', 'error');
            }
        }
    }
}

// 全局实例
const parkingManagement = new ParkingManagement();

// 全局函数供HTML调用
function closeModal() {
    parkingManagement.closeModal();
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 组件加载完成后初始化
    setTimeout(() => {
        parkingManagement.checkUrlParams();
    }, 100);
});