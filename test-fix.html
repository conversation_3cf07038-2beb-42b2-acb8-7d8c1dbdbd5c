<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div id="header"></div>
    
    <main class="ml-0 lg:ml-64 pt-16 p-6">
        <div class="max-w-4xl mx-auto">
            <div class="glass-card p-6 rounded-2xl mb-8">
                <h1 class="text-2xl font-bold mb-6">修复测试页面</h1>
                
                <div class="space-y-4">
                    <div>
                        <h2 class="text-lg font-semibold mb-3">状态检查</h2>
                        <div class="bg-gray-800/50 rounded-lg p-4">
                            <p>Storage状态: <span id="storageStatus" class="font-mono text-blue-400">检查中...</span></p>
                            <p>ComponentLoader状态: <span id="componentStatus" class="font-mono text-green-400">检查中...</span></p>
                            <p>停车场数量: <span id="parkingCount" class="font-mono text-yellow-400">0</span></p>
                        </div>
                    </div>
                    
                    <div>
                        <h2 class="text-lg font-semibold mb-3">快速操作</h2>
                        <div class="flex space-x-4">
                            <button onclick="addSampleData()" class="btn-primary">添加示例数据</button>
                            <button onclick="checkStatus()" class="btn-secondary">检查状态</button>
                            <button onclick="clearData()" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg transition-colors">清空数据</button>
                        </div>
                    </div>
                    
                    <div>
                        <h2 class="text-lg font-semibold mb-3">测试结果</h2>
                        <div id="testResults" class="bg-gray-800/50 rounded-lg p-4">
                            <!-- 测试结果将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function addSampleData() {
            // 添加示例停车场
            storage.addParkingLot({
                name: 'A区地下停车场',
                location: 'B1层',
                totalSpaces: 50,
                hourlyRate: 5
            });
            
            storage.addParkingLot({
                name: 'B区地面停车场',
                location: '广场东侧',
                totalSpaces: 30,
                hourlyRate: 3
            });
            
            checkStatus();
            componentLoader.showToast('示例数据添加成功', 'success');
        }
        
        function checkStatus() {
            const storageStatus = typeof storage !== 'undefined' ? '✓ 已加载' : '✗ 未加载';
            const componentStatus = typeof componentLoader !== 'undefined' ? '✓ 已加载' : '✗ 未加载';
            const parkingCount = typeof storage !== 'undefined' ? storage.getParkingLots().length : 0;
            
            document.getElementById('storageStatus').textContent = storageStatus;
            document.getElementById('componentStatus').textContent = componentStatus;
            document.getElementById('parkingCount').textContent = parkingCount;
            
            // 检查header是否加载
            const headerElement = document.getElementById('header');
            const headerContent = headerElement.innerHTML;
            
            let results = `
                <div class="space-y-2">
                    <p>Header加载状态: ${headerContent.includes('智慧停车场管理系统') ? '✓ 成功' : '✗ 失败'}</p>
                    <p>Header内容长度: ${headerContent.length} 字符</p>
            `;
            
            if (typeof window.parkingSwitcher !== 'undefined') {
                results += `<p class="text-green-400">✓ 停车场切换器已初始化</p>`;
            } else {
                results += `<p class="text-red-400">✗ 停车场切换器未初始化</p>`;
            }
            
            results += '</div>';
            document.getElementById('testResults').innerHTML = results;
        }
        
        function clearData() {
            if (confirm('确定要清空所有数据吗？')) {
                localStorage.clear();
                location.reload();
            }
        }
        
        // 页面加载完成后检查
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                checkStatus();
                
                // 尝试初始化停车场切换器
                if (typeof ParkingSwitcher !== 'undefined' && typeof storage !== 'undefined' && typeof componentLoader !== 'undefined') {
                    if (!window.parkingSwitcher) {
                        window.parkingSwitcher = new ParkingSwitcher();
                        setTimeout(checkStatus, 500);
                    }
                }
            }, 1000);
        });
    </script>
</body>
</html>