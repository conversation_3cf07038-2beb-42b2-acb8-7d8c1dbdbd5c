<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车场管理 - 智慧停车场系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div id="header"></div>
    <div id="sidebar"></div>
    
    <main class="ml-0 lg:ml-64 pt-16 p-6">
        <div class="max-w-6xl mx-auto">
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h1 class="text-2xl font-bold">停车场管理</h1>
                    <p class="text-gray-400">管理所有停车场信息和设置</p>
                </div>
                <button id="addParkingBtn" class="btn-primary">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    添加停车场
                </button>
            </div>

            <!-- 停车场列表 -->
            <div class="glass-card p-6 rounded-2xl mb-8">
                <h2 class="text-xl font-semibold mb-6">停车场列表</h2>
                <div id="parkingList" class="space-y-4">
                    <!-- 停车场列表将通过JS动态生成 -->
                </div>
            </div>

            <!-- 添加/编辑停车场模态框 -->
            <div id="parkingModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden items-center justify-center z-50 p-4">
                <div class="glass-card p-6 rounded-2xl w-full max-w-md">
                    <h3 class="text-xl font-semibold mb-4" id="modalTitle">添加停车场</h3>
                    
                    <form id="parkingForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">停车场名称</label>
                            <input type="text" name="name" required 
                                   class="input-glass w-full" 
                                   placeholder="例如：A区地下停车场">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">位置描述</label>
                            <input type="text" name="location" 
                                   class="input-glass w-full" 
                                   placeholder="例如：B1层东侧">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">总车位数</label>
                            <input type="number" name="totalSpaces" required min="1"
                                   class="input-glass w-full" 
                                   placeholder="例如：50">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">小时费率 (元)</label>
                            <input type="number" name="hourlyRate" required min="0" step="0.5"
                                   class="input-glass w-full" 
                                   placeholder="例如：5.0">
                        </div>

                        <div class="flex space-x-4 pt-4">
                            <button type="button" onclick="closeModal()" 
                                    class="btn-secondary flex-1">取消</button>
                            <button type="submit" class="btn-primary flex-1">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/parking-management.js"></script>
</body>
</html>