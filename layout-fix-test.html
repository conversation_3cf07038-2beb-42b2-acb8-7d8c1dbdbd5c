<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局修复测试 - 智慧停车场系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div id="header"></div>
    <div id="sidebar"></div>
    
    <main class="ml-0 lg:ml-64 pt-16 p-6">
        <div class="max-w-7xl mx-auto">
            <!-- 测试内容区域 -->
            <div class="glass-card p-6 rounded-2xl mb-8">
                <h1 class="text-2xl font-bold mb-4">布局修复测试页面</h1>
                <p class="text-gray-300 mb-4">这个页面用于测试主内容区域是否被header或其他元素遮挡。</p>
                
                <div class="bg-red-500/20 border border-red-500 rounded-lg p-4 mb-4">
                    <h2 class="text-lg font-semibold text-red-400 mb-2">⚠️ 重要测试点</h2>
                    <ul class="text-sm text-gray-300 space-y-1">
                        <li>• 这个红色区域应该完全可见，不被header遮挡</li>
                        <li>• 页面顶部应该有足够的空白间距</li>
                        <li>• 停车场切换器下拉菜单不应遮挡主内容</li>
                        <li>• 在不同屏幕尺寸下都应该正常显示</li>
                    </ul>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    <div class="glass-card p-4 rounded-xl">
                        <h3 class="font-semibold mb-2">桌面端测试</h3>
                        <p class="text-sm text-gray-300">在大屏幕设备上，主内容应该有左边距以避开sidebar</p>
                    </div>
                    <div class="glass-card p-4 rounded-xl">
                        <h3 class="font-semibold mb-2">平板端测试</h3>
                        <p class="text-sm text-gray-300">在平板设备上，sidebar通常隐藏，主内容应该占满宽度</p>
                    </div>
                    <div class="glass-card p-4 rounded-xl">
                        <h3 class="font-semibold mb-2">移动端测试</h3>
                        <p class="text-sm text-gray-300">在手机上，布局应该完全响应式，内容不被遮挡</p>
                    </div>
                </div>
                
                <div class="bg-green-500/20 border border-green-500 rounded-lg p-4 mb-4">
                    <h2 class="text-lg font-semibold text-green-400 mb-2">✅ 修复内容</h2>
                    <ul class="text-sm text-gray-300 space-y-1">
                        <li>• 增加了主内容区域的顶部padding (pt-20 → pt-20)</li>
                        <li>• 修复了下拉菜单的z-index层级问题</li>
                        <li>• 优化了响应式布局，确保不同屏幕尺寸下的正确显示</li>
                        <li>• 添加了强制样式以覆盖可能的冲突</li>
                    </ul>
                </div>
                
                <div class="bg-blue-500/20 border border-blue-500 rounded-lg p-4">
                    <h2 class="text-lg font-semibold text-blue-400 mb-2">🔧 测试步骤</h2>
                    <ol class="text-sm text-gray-300 space-y-1 list-decimal list-inside">
                        <li>检查页面顶部是否有足够空白，红色警告框完全可见</li>
                        <li>悬停在header的停车场切换器上，确认下拉菜单不遮挡内容</li>
                        <li>调整浏览器窗口大小，测试响应式布局</li>
                        <li>在移动端模拟器中测试布局</li>
                        <li>确认sidebar在大屏幕上不遮挡主内容</li>
                    </ol>
                </div>
            </div>
            
            <!-- 额外的测试内容 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="glass-card p-6 rounded-2xl">
                    <h2 class="text-xl font-semibold mb-4">左侧测试区域</h2>
                    <div class="space-y-4">
                        <div class="h-20 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center">
                            <span class="text-sm">测试内容块 1</span>
                        </div>
                        <div class="h-20 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-lg flex items-center justify-center">
                            <span class="text-sm">测试内容块 2</span>
                        </div>
                        <div class="h-20 bg-gradient-to-r from-yellow-500/20 to-red-500/20 rounded-lg flex items-center justify-center">
                            <span class="text-sm">测试内容块 3</span>
                        </div>
                    </div>
                </div>
                
                <div class="glass-card p-6 rounded-2xl">
                    <h2 class="text-xl font-semibold mb-4">右侧测试区域</h2>
                    <div class="space-y-4">
                        <div class="h-32 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-lg flex items-center justify-center">
                            <span class="text-sm">这个区域应该完全可见</span>
                        </div>
                        <div class="h-32 bg-gradient-to-br from-indigo-500/20 to-blue-500/20 rounded-lg flex items-center justify-center">
                            <span class="text-sm">不应该被任何元素遮挡</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script>
        // 简单的测试脚本
        document.addEventListener('DOMContentLoaded', function() {
            console.log('布局修复测试页面已加载');
            
            // 检查主内容区域的样式
            const main = document.querySelector('main');
            if (main) {
                const styles = window.getComputedStyle(main);
                console.log('主内容区域样式:', {
                    paddingTop: styles.paddingTop,
                    marginLeft: styles.marginLeft,
                    position: styles.position
                });
            }
            
            // 检查header的z-index
            const header = document.querySelector('header');
            if (header) {
                const styles = window.getComputedStyle(header);
                console.log('Header样式:', {
                    zIndex: styles.zIndex,
                    position: styles.position,
                    height: header.offsetHeight + 'px'
                });
            }
        });
    </script>
</body>
</html>
