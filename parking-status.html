<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车位状态监控 - 智慧停车场系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen text-white">
    <div id="header"></div>
    <div id="sidebar"></div>
    
    <main class="ml-0 lg:ml-64 pt-16 p-6">
        <div class="max-w-7xl mx-auto">
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h1 class="text-2xl font-bold">车位状态监控</h1>
                    <p class="text-gray-400">实时查看停车场车位使用情况</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="parkingSelectorContainer">
                        <!-- 停车场选择器将通过JS动态生成 -->
                    </div>
                    <button onclick="refreshData()" class="btn-secondary">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新
                    </button>
                </div>
            </div>

            <!-- 统计信息卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" id="statsCards">
                <!-- 统计卡片将通过JS动态生成 -->
            </div>

            <!-- 车位状态网格 -->
            <div class="glass-card p-6 rounded-2xl mb-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold" id="gridTitle">车位状态</h2>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-green-400 rounded"></div>
                            <span class="text-sm text-gray-300">空闲</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-red-400 rounded"></div>
                            <span class="text-sm text-gray-300">占用</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-yellow-400 rounded"></div>
                            <span class="text-sm text-gray-300">预定</span>
                        </div>
                    </div>
                </div>

                <div id="parkingGrid" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                    <!-- 车位网格将通过JS动态生成 -->
                </div>
            </div>

            <!-- 当前停放车辆 -->
            <div class="glass-card p-6 rounded-2xl">
                <h2 class="text-xl font-semibold mb-4">当前停放车辆</h2>
                <div id="currentVehicles" class="space-y-3">
                    <!-- 车辆列表将通过JS动态生成 -->
                </div>
            </div>
        </div>
    </main>

    <!-- 车位详情模态框 -->
    <div id="spaceModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden items-center justify-center z-50 p-4">
        <div class="glass-card p-6 rounded-2xl w-full max-w-md">
            <h3 class="text-xl font-semibold mb-4" id="modalSpaceTitle">车位详情</h3>
            <div id="spaceDetails">
                <!-- 详情内容将通过JS动态生成 -->
            </div>
            <div class="flex justify-end pt-4">
                <button onclick="closeSpaceModal()" class="btn-secondary">关闭</button>
            </div>
        </div>
    </div>

    <script src="js/storage.js"></script>
    <script src="js/components.js"></script>
    <script src="js/parking-status.js"></script>
</body>
</html>